@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', sans-serif;
}

html,
body {
  width: 100%;
}

body {
  font-family: 'Inter', sans-serif;
}

.container {
  padding: 0;
  margin: 0 auto;
}

.main-content {
  margin-left: 250px;
  transition: margin-left 0.3s ease;
}

/* PrimeNG button styling - primary severity */
.p-button.p-button-primary {
  background-color: var(--primary-blue, #1175BD) !important;
  border-color: var(--primary-blue, #1175BD) !important;
}

.p-button.p-button-primary:hover {
  background-color: var(--primary-blue-hover, #0F65A3) !important;
  border-color: var(--primary-blue-hover, #0F65A3) !important;
}

/* Global Breadcrumb Styling */
.p-breadcrumb {
  background: transparent !important;
  border: none !important;
}

.p-breadcrumb .p-breadcrumb-list .p-breadcrumb-item .p-breadcrumb-item-link,
.p-breadcrumb .p-breadcrumb-list .p-breadcrumb-item .p-breadcrumb-item-label,
.p-breadcrumb .p-breadcrumb-list .p-breadcrumb-item .p-breadcrumb-item-icon,
.p-breadcrumb .p-breadcrumb-list .p-breadcrumb-home-item .p-breadcrumb-item-link,
.p-breadcrumb .p-breadcrumb-list .p-breadcrumb-home-item .p-breadcrumb-item-icon {
  color: white !important;
}

.p-breadcrumb .p-breadcrumb-list .p-breadcrumb-separator svg {
  fill: white !important;
  color: white !important;
}

/* Global z-index fixes for confirmation dialogs to overlay sidebar */
.p-dialog-mask {
  z-index: 1300 !important;
  /* Higher than sidebar z-index */
}

.p-confirmdialog {
  z-index: 1301 !important;
  /* Higher than mask to be visible */
}

.p-dialog-mask.p-component-overlay {
  /* This prevents sidebar interaction when dialog is shown */
  pointer-events: auto !important;
}

