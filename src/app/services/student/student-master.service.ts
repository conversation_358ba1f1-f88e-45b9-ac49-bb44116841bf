import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment.development';

@Injectable({
  providedIn: 'root'
})
export class StudentMasterService {
  private readonly baseUrl = environment.apiUrl + 'StudentMaster/';
  
  private readonly http: HttpClient = inject(HttpClient);

  
  checkValidStudentId(studentId: string): Observable<any> {
    return this.http.get(
      `${this.baseUrl}CheckValidStudent/${studentId}`,
      {
        observe: 'response',
      }
    );
  }
}
