import { Injectable, inject } from '@angular/core';
import {
  HttpClient,
  HttpErrorResponse,
  HttpResponse,
  HttpParams,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  TrainingOrganizationRegistrationNew,
  TrainingOrganizationResponse,
  TrainingOrganizationViewData,
} from '../../../models/shared/training/training-organization.model';
import { environment } from '../../../../environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class TrainingOrganizationService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = `${environment.apiUrl}TrainingOrganization`;

  /**
   * Register a new training organization
   * @param organizationData - Training organization registration data
   * @returns Observable with HTTP response
   */
  registerTrainingOrganization(
    organizationData: TrainingOrganizationRegistrationNew
  ): Observable<HttpResponse<TrainingOrganizationResponse>> {
    return this.http
      .post<TrainingOrganizationResponse>(
        `${this.baseUrl}/RegisterTrainingOrganization`,
        organizationData,
        { observe: 'response' }
      )
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Update an existing training organization
   * @param id - Training organization ID
   * @param organizationData - Updated training organization data
   * @returns Observable with HTTP response
   */
  updateTrainingOrganization(
    id: string,
    organizationData: TrainingOrganizationRegistrationNew
  ): Observable<HttpResponse<TrainingOrganizationResponse>> {
    return this.http
      .patch<TrainingOrganizationResponse>(
        `${this.baseUrl}/UpdateTrainingOrganization/${id}`,
        organizationData,
        { observe: 'response' }
      )
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Delete a training organization
   * @param id - Training organization ID to delete
   * @returns Observable with HTTP response
   */
  deleteTrainingOrganization(id: string): Observable<HttpResponse<void>> {
    return this.http
      .delete<void>(`${this.baseUrl}/DeleteTrainingOrganization/${id}`, {
        observe: 'response',
      })
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Get training organization by ID
   * @param id - Training organization ID
   * @returns Observable with organization details
   */
  getTrainingOrganizationById(
    id: string
  ): Observable<TrainingOrganizationResponse> {
    return this.http
      .get<TrainingOrganizationResponse>(
        `${this.baseUrl}/GetTrainingOrganizationById/${id}`
      )
      .pipe(
        map((response: TrainingOrganizationResponse) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Get training organization by organization ID
   * @param organizationId - Organization ID
   * @returns Observable with organization details
   */
  getTrainingOrganizationByOrgId(
    organizationId: string
  ): Observable<TrainingOrganizationResponse> {
    return this.http
      .get<TrainingOrganizationResponse>(
        `${this.baseUrl}/GetTrainingOrganizationByOrgId/${organizationId}`
      )
      .pipe(
        map((response: TrainingOrganizationResponse) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Get all training organizations as queryable
   * @returns Observable with list of organizations
   */
  getTrainingOrganizationsAsQueryable(): Observable<
    TrainingOrganizationResponse[]
  > {
    return this.http
      .get<TrainingOrganizationResponse[]>(
        `${this.baseUrl}/GetTrainingOrganizationsAsQueryable`
      )
      .pipe(
        map((response: TrainingOrganizationResponse[]) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Get all training organizations
   * @returns Observable with list of organizations
   */
  getTrainingOrganizations(): Observable<TrainingOrganizationResponse[]> {
    return this.http
      .get<TrainingOrganizationResponse[]>(
        `${this.baseUrl}/GetTrainingOrganizations`
      )
      .pipe(
        map((response: TrainingOrganizationResponse[]) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Check if organization exists
   * @param organizationId - Organization ID to check
   * @returns Observable with existence status
   */
  organizationExists(organizationId: string): Observable<boolean> {
    return this.http
      .get<boolean>(`${this.baseUrl}/OrganizationExists/${organizationId}`)
      .pipe(
        map((response: boolean) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Get training organizations created by user ID with pagination
   * @param pageNumber - Page number for pagination
   * @param pageSize - Number of items per page
   * @returns Observable with paginated list of organizations
   */
  getTrainingOrganizationsAsyncCreatedByUserId(
    pageNumber: number,
    pageSize: number
  ): Observable<HttpResponse<TrainingOrganizationViewData[]>> {
    const params = new HttpParams()
      .set('PageNumber', pageNumber.toString())
      .set('PageSize', pageSize.toString());

    return this.http
      .get<TrainingOrganizationViewData[]>(
        `${this.baseUrl}/GetTrainingOrganizationsAsyncCreatedByUserId`,
        {
          params,
          observe: 'response',
        }
      )
      .pipe(catchError((error) => this.handleError(error)));
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   * @returns Observable error
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Bad Request: Please check your input data';
          break;
        case 401:
          errorMessage = 'Unauthorized: Please log in again';
          break;
        case 403:
          errorMessage =
            'Forbidden: You do not have permission to perform this action';
          break;
        case 404:
          errorMessage =
            'Not Found: The requested training organization was not found';
          break;
        case 409:
          errorMessage =
            'Conflict: A training organization with this ID already exists';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Please try again later';
          break;
        default:
          errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('Training Organization Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
