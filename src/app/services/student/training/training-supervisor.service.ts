import { Injectable } from '@angular/core';
import { Observable, of, delay } from 'rxjs';

// Interface for supervisor registration response
interface SupervisorRegistrationResponse {
  hasRegistration: boolean;
  data?: SupervisorRegistration;
}

// Interface for supervisor registration data
interface SupervisorRegistration {
  id: string;
  name: string;
  designation: string;
  contactNumber: string;
  location: string;
  email: string;
  memberships: string[];
  approvalStatus: 'pending' | 'approved' | 'rejected';
  registeredAt: Date;
  lastUpdated: Date;
  rejectionReason?: string;
}

@Injectable({
  providedIn: 'root'
})
export class TrainingSupervisorService {

  constructor() { }

  /**
   * Check if a user has supervisor registration
   * @param username - The username to check registration for
   * @returns Observable with registration status and data
   */
  checkSupervisorRegistrationStatus(username: string): Observable<SupervisorRegistrationResponse> {
    // Mock data for testing - replace with actual API call
    const mockData: SupervisorRegistration = {
      id: 'SUP001',
      name: '<PERSON>',
      designation: 'Senior Accountant',
      contactNumber: '**********',
      location: 'New York, NY',
      email: '<EMAIL>',
      memberships: ['ACCA', 'CPA'],
      approvalStatus: 'approved',
      registeredAt: new Date('2024-01-15'),
      lastUpdated: new Date('2024-01-15'),
      rejectionReason: undefined
    };

    // Simulate different scenarios for testing
    const hasRegistration = Math.random() > 0.3; // 70% chance of having registration
    
    const response: SupervisorRegistrationResponse = {
      hasRegistration,
      data: hasRegistration ? mockData : undefined
    };

    // Simulate API delay
    return of(response).pipe(delay(1000));
  }

  /**
   * Get supervisor details by ID
   * @param supervisorId - The supervisor ID
   * @returns Observable with supervisor details
   */
  getSupervisorDetails(supervisorId: string): Observable<SupervisorRegistration> {
    // Mock data for testing - replace with actual API call
    const mockData: SupervisorRegistration = {
      id: supervisorId,
      name: 'John Doe',
      designation: 'Senior Accountant',
      contactNumber: '**********',
      location: 'New York, NY',
      email: '<EMAIL>',
      memberships: ['ACCA', 'CPA', 'ACA'],
      approvalStatus: 'approved',
      registeredAt: new Date('2024-01-15'),
      lastUpdated: new Date('2024-01-20'),
      rejectionReason: undefined
    };

    return of(mockData).pipe(delay(800));
  }
}
