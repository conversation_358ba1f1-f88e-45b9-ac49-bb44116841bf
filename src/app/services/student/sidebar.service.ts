import { Injectable } from '@angular/core';
import { MenuItem } from '../../models/shared/menu-item.model';

@Injectable({
    providedIn: 'root'
})
export class SidebarService {

    getMenuItems(): MenuItem[] {
        return [
            {
                label: 'Dashboard',
                icon: 'pi pi-chart-line',
                routerLink: '/dashboard'
            },
            {
                label: 'Job Bank',
                icon: 'pi pi-briefcase',
                routerLink: '/dashboard/aat-job-bank'
            },
            {
                label: 'Training',
                icon: 'pi pi-graduation-cap',
                items: [
                    {
                        label: 'Supervisor',
                        icon: 'pi pi-user-plus',
                        routerLink: '/dashboard/training/training-supervisor'
                    },
                    {
                        label: 'Organization',
                        icon: 'pi pi-building',
                        routerLink: '/dashboard/training/training-organization'
                    },
                ],
            },
            {
                label: 'Registrations',
                icon: 'pi pi-calendar',
            },
            {
                label: 'Exams',
                icon: 'pi pi-pencil',
                routerLink: '/attendance'
            },
            {
                label: 'Exemptions',
                icon: 'pi pi-fast-forward',
                items: [
                    {
                        label: 'Request Exemption',
                        icon: 'pi pi-plus',
                        routerLink: '/exams'
                    },
                    {
                        label: 'View Exemptions',
                        icon: 'pi pi-list',
                        routerLink: '/exams/new'
                    }
                ]
            },
            {
                label: 'Education & Training',
                icon: 'pi pi-calculator',
                routerLink: '/attendance'
            },
            {
                label: 'Activity',
                icon: 'pi pi-book',
                routerLink: '/library'
            },
            {
                label: 'Settings',
                icon: 'pi pi-cog',
                items: [
                    {
                        label: 'Profile',
                        icon: 'pi pi-user',
                        routerLink: '/settings/profile'
                    },
                    {
                        label: 'Security',
                        icon: 'pi pi-lock',
                        routerLink: '/settings/security'
                    }
                ]
            },
            {
                label: 'Help',
                icon: 'pi pi-question',
                routerLink: '/dashboard/help'
            },
        ];
    }
}
