import { Injectable } from '@angular/core';
import { MenuItem } from '../../models/shared/menu-item.model';

@Injectable({
    providedIn: 'root'
})
export class SidebarService {

    getMenuItems(): MenuItem[] {
        return [
            {
                label: 'Dashboard',
                icon: 'pi pi-chart-line',
                routerLink: '/dashboard'
            },
            {
                label: 'Registrations',
                icon: 'pi pi-calendar',
                routerLink: '/attendance'
            },
            {
                label: 'Exams',
                icon: 'pi pi-pencil',
                routerLink: '/attendance'
            },
            {
                label: 'Exemptions',
                icon: 'pi pi-fast-forward',
                items: [
                    {
                        label: 'Request Exemption',
                        icon: 'pi pi-plus',
                        routerLink: '/exams'
                    },
                    {
                        label: 'View Exemptions',
                        icon: 'pi pi-list',
                        routerLink: '/exams/new'
                    }
                ]
            },
            {
                label: 'Education & Training',
                icon: 'pi pi-calculator',
                routerLink: '/attendance'
            },
            {
                label: 'Activity',
                icon: 'pi pi-book',
                routerLink: '/library'
            },
            {
                label: 'Settings',
                icon: 'pi pi-cog',
                items: [
                    {
                        label: 'Profile',
                        icon: 'pi pi-user',
                        routerLink: ''
                    },
                    {
                        label: 'Security',
                        icon: 'pi pi-lock',
                        routerLink: ''
                    }
                ]
            }
        ];
    }
}
