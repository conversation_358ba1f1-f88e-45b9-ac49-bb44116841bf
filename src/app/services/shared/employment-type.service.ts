import { HttpClient, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { EmploymentType } from '../../models/shared/employment-type';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class EmploymentTypeService {
    private readonly http = inject(HttpClient);
    private readonly baseUrl: string = `${environment.apiUrl}EmploymentType/`; 

    public addUpdateEmploymentType(employmentType: EmploymentType): Observable<HttpResponse<Object>> {
        return this.http.post<Object>(`${this.baseUrl}AddEmploymentType`, employmentType, { observe: 'response' });
    }

    public deleteEmploymentType(id: string): Observable<HttpResponse<Object>> {
        return this.http.delete<Object>(`${this.baseUrl}${id}`, { observe: 'response' });
    }
    public getEmploymentTypes(): Observable<EmploymentType[]> {
        return this.http.get<EmploymentType[]>(`${this.baseUrl}GetAll`);
    }
    public getEmploymentTypeById(id: string): Observable<EmploymentType> {
        return this.http.get<EmploymentType>(`${this.baseUrl}GetEmploymetType/${id}`);
    }
}
