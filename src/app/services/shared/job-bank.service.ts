import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment.development';
import { JobBank, JobBankUpdate, JobBankView } from '../../models/shared/job-bank/job-bank';

export interface Job {
  id: number;
  title: string;
  description: string;
  image: string;
}

@Injectable({
  providedIn: 'root',
})
export class JobBankService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl: string = `${environment.apiUrl}JobBank/`;

  public getJobBanks(
    pageNumber: number,
    pageSize: number
  ): Observable<HttpResponse<JobBankView[]>> {
    return this.http.get<JobBankView[]>(
      `${this.baseUrl}GetJobListPaged?pageNumber=${pageNumber}&pageSize=${pageSize}`,
      { observe: 'response' }
    );
  }

  public getJobBankById(id: string): Observable<JobBankView> {
    return this.http.get<JobBankView>(`${this.baseUrl}${id}`);
  }

  public addJobBank(jobBank: JobBank): Observable<HttpResponse<Object>> {
    return this.http.post<Object>(`${this.baseUrl}AddJob`, jobBank, {
      observe: 'response',
    });
  }

  public updateJobBank(jobBank: JobBankUpdate): Observable<HttpResponse<Object>> {
    return this.http.patch<Object>(`${this.baseUrl}UpdateJob`, jobBank, {
      observe: 'response',
    });
  }

  public deleteJobBank(id: string): Observable<HttpResponse<Object>> {
    return this.http.delete<Object>(`${this.baseUrl}${id}`, {
      observe: 'response',
    });
  }

  searchJobs(
    pageNumber: number,
    pageSize: number,
    searchTerm: any
  ): Observable<HttpResponse<JobBankView[]>> {
    return this.http.get<JobBankView[]>(
      `${this.baseUrl}SearchJobs?PageNumber=${pageNumber}&PageSize=${pageSize}&Search=${searchTerm}`,
      { observe: 'response' }
    );
  }

  searchJobsWithFilters(
    pageNumber: number,
    pageSize: number,
    searchTerm: string,
    employmentTypes: number[],
    location: string,
    jobCategory: string
  ): Observable<HttpResponse<JobBankView[]>> {

    let params = new HttpParams()
      .set('PageNumber', pageNumber.toString())
      .set('PageSize', pageSize.toString());

    // Add search filters only if they have values
    if (searchTerm) {
      params = params.set('jobTitle', searchTerm);
    }

    if (employmentTypes && employmentTypes.length > 0) {
      params = params.set('employementType', employmentTypes.join(''));
    }

    if (location) {
      params = params.set('jobLocation', location);
    }

    if (jobCategory) {
      params = params.set('jobCategory', jobCategory);
    }

    return this.http.get<JobBankView[]>(`${this.baseUrl}GetJobListPagedAsync`, {
      observe: 'response',
      params: params,
    });
  }
}
