import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { Qualification } from '../../models/shared/supervisor/qualification';

@Injectable({
  providedIn: 'root'
})
export class SupervisorService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = 'your_api_url_here';

  //mock data
  private readonly mockQualifications: Qualification[] = [
    { qualificationId: 'ca', qualification: 'Chartered Accountant (CA)' },
    { qualificationId: 'cma', qualification: 'Cost and Management Accountant (CMA)' },
    { qualificationId: 'cs', qualification: 'Company Secretary (CS)' },
    { qualificationId: 'cpa', qualification: 'Certified Public Accountant (CPA)' },
    { qualificationId: 'acca', qualification: 'Association of Chartered Certified Accountants (ACCA)' },
    { qualificationId: 'cfa', qualification: 'Chartered Financial Analyst (CFA)' }
  ];

  getMemberships(): Observable<Qualification[]> {
    // Return mock data for testing instead of API call
    return of(this.mockQualifications);
  }

  uploadSupervisorDocuments(supervisorData: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/supervisor/register`, supervisorData, {
      observe: 'response'
    });
  }

  uploadMultipleFiles(supervisorData: any): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/supervisor/upload-files`, supervisorData, {
      observe: 'response'
    });
  }
}
