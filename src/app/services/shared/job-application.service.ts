import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { JobApplication } from '../../models/admin/job-bank/job-application';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class JobApplicationService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl: string = `${environment.apiUrl}JobApplications`;

  public submitJobApplication(
    formData: FormData
  ): Observable<HttpResponse<Object>> {
    return this.http.post<Object>(`${this.baseUrl}`, formData, {
      observe: 'response',
    });
  }

  public getAllJobApplications(
    pageNumber: number,
    pageSize: number
  ): Observable<HttpResponse<JobApplication[]>> {
    let params = new HttpParams()
      .set('PageNumber', pageNumber.toString())
      .set('PageSize', pageSize.toString());

    return this.http.get<JobApplication[]>(
      `${this.baseUrl}/GetJobApplicationListPaged`,
      {
        observe: 'response',
        params: params,
      }
    );
  }

  searchApplications(
    pageNumber: number,
    pageSize: number,
    searchTerm: string
  ): Observable<HttpResponse<JobApplication[]>> {
    let params = new HttpParams();
    params = params.append('pageNumber', pageNumber);
    params = params.append('pageSize', pageSize);
    params = params.append('search', searchTerm);

    return this.http.get<JobApplication[]>(`${this.baseUrl}/SearchJobApplicationsPaged`, {
      observe: 'response',
      params,
    });
  }

  public checkApplicationSubmission(
    jobId: string
  ): Observable<HttpResponse<Object>> {
    return this.http.get(`${this.baseUrl}/HasApplied`, {
      observe: 'response',
      params: new HttpParams().set('jobId', jobId),
    });
  }
}
