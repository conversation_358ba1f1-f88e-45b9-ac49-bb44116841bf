import { HttpClient, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from '../../../environments/environment.development';
import { Observable, tap } from 'rxjs';
import Cookies from 'js-cookie';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  User,
  UserRole,
} from '../../models/auth/auth';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private readonly http: HttpClient = inject(HttpClient);
  private readonly router = inject(Router);
  private readonly baseUrl = environment.apiUrl + 'Auth/';
  currentUser: User = {} as User;

  async login(loginRequest: LoginRequest): Promise<Observable<LoginResponse>> {
    return this.http
      .post<LoginResponse>(`${this.baseUrl}Login`, loginRequest, {
        responseType: 'json',
        headers: new HttpHeaders({
          'Content-Type': 'application/json',
        }),
      })
      .pipe(
        tap((response) => {
          this.currentUser = {
            id: response.id,
            username: response.username,
            roles: response.roles,
          };
          localStorage.setItem('user', JSON.stringify(this.currentUser));
          Cookies.set('token', response.token, {
            expires: new Date(response.expiryTime),
          });
        })
      );
  }

  register(RegisterRequest: RegisterRequest): Observable<any> {
    return this.http.post(this.baseUrl + 'Register', RegisterRequest, {
      observe: 'response',
    });
  }

  logout(): void {
    Cookies.remove('token');
    localStorage.removeItem('user');
    this.router.navigate(['/']);
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    // Check if token is expired
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const isExpired = payload.exp * 1000 <= Date.now();

      if (isExpired) {
        this.logout();
        return false;
      }

      return true;
    } catch (error) {
      this.logout();
      return false;
    }
  }

  getToken(): string | null {
    return Cookies.get('token') ?? null;
  }

  getCurrentUser(): User | null {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      const parsedUser: User = JSON.parse(savedUser);
      return parsedUser;
    }
    return null;
  }

  getOtp(email: string, otp: string): Observable<any> {
    return this.http.post(
      `${this.baseUrl}Verify-OTP?email=${email}&otp=${otp}`,
      {
        observe: 'response',
      }
    );
  }

  resendOtp(email: string): Observable<any> {
    return this.http.post(`${this.baseUrl}Resend-OTP?email=${email}`, {
      observe: 'response',
    });
  }

  /**
   * Check if the current user has any of the specified roles
   * @param roles Array of roles to check against
   * @returns boolean indicating if user has any of the roles
   */
  hasRole(roles: UserRole[]): boolean {
    const currentUser = this.getCurrentUser();
    if (!currentUser || !currentUser.roles) {
      return false;
    }
    return roles.some(role => currentUser.roles.includes(role));
  }

  /**
   * Check if the current user has a specific role
   * @param role The role to check for
   * @returns boolean indicating if user has the role
   */
  hasSpecificRole(role: UserRole): boolean {
    return this.hasRole([role]);
  }
}
