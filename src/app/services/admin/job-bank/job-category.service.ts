import { inject, Injectable } from '@angular/core';
import { Observable, throwError, catchError, map } from 'rxjs';
import { JobCategory } from '../../../models/shared/job-bank/job-category';
import { environment } from '../../../../environments/environment.development';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
  HttpResponse,
} from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class JobCategoryService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = `${environment.apiUrl}JobCategory/`;

  getCategories(): Observable<HttpResponse<JobCategory[]>> {
    return this.http.get<JobCategory[]>(`${this.baseUrl}GetAllJobCategories`, { observe: "response" });
  }

  getAllJobCategoriesNoPageAsync(): Observable<HttpResponse<JobCategory[]>> {
    return this.http.get<JobCategory[]>(`${this.baseUrl}GetAllJobCategoriesNoPageAsync`, { observe: "response" });
  }

  getCategoriesPaged(
    pageNumber: number,
    pageSize: number
  ): Observable<HttpResponse<JobCategory[]>> {
    let params = new HttpParams();

    if (pageNumber && pageSize) {
      params = params.append('pageNumber', pageNumber);
      params = params.append('pageSize', pageSize);
    }

    return this.http.get<JobCategory[]>(`${this.baseUrl}GetAllJobCategories`, {
      observe: 'response',
      params,
    });
  }

  addCategory(
    categoryData: JobCategory
  ): Observable<HttpResponse<JobCategory>> {
    return this.http
      .post<JobCategory>(`${this.baseUrl}AddJobCategory`, categoryData, {
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<JobCategory>) => response),
        catchError((error) => this.handleError(error))
      );
  }

  updateCategory(
    categoryData: JobCategory
  ): Observable<HttpResponse<JobCategory>> {
    return this.http
      .patch<JobCategory>(`${this.baseUrl}Update`, categoryData, {
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<JobCategory>) => response),
        catchError((error) => this.handleError(error))
      );
  }

  deleteCategory(id: string): Observable<HttpResponse<JobCategory>> {
    return this.http
      .delete<JobCategory>(`${this.baseUrl}Delete/${id}`, { observe: 'response' })
      .pipe(
        map((response: HttpResponse<JobCategory>) => response),
        catchError((error) => this.handleError(error))
      );
  }

  getCategoryById(id: number): Observable<HttpResponse<JobCategory>> {
    return this.http
      .get<JobCategory>(`${this.baseUrl}${id}`, {
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<JobCategory>) => response),
        catchError((error) => this.handleError(error))
      );
  }

  private handleError(
    error: HttpErrorResponse
  ): Observable<HttpResponse<JobCategory>> {
    let errorMessage;

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client-side error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server error (${error.status}): ${error.error.title}`;
    }

    console.error('Error processing Job Category Data:', errorMessage);

    // Return an Observable with an error HttpResponse
    return throwError(
      () => new HttpResponse<JobCategory>({ status: error.status, body: null })
    );
  }
}
