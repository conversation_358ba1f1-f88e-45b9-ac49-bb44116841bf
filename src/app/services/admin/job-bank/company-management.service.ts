import { inject, Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment.development';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
  HttpResponse,
} from '@angular/common/http';
import { Company, CompanyWithLogo } from '../../../models/admin/job-bank/company';
import { catchError, map, Observable, throwError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CompanyManagementService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = `${environment.apiUrl}Company/`;

  addCompany(companyData: CompanyWithLogo): Observable<HttpResponse<CompanyWithLogo>> {
    return this.http
      .post<CompanyWithLogo>(`${this.baseUrl}CompanyRegister`, companyData, {
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<CompanyWithLogo>) => response),
        catchError((error) => this.handleErrorWithLogo(error))
      );
  }

  getCompaniesPaged(
    pageNumber: number,
    pageSize: number
  ): Observable<HttpResponse<Company[]>> {
    let params = new HttpParams();

    if (pageNumber && pageSize) {
      params = params.append('pageNumber', pageNumber);
      params = params.append('pageSize', pageSize);
    }

    return this.http.get<Company[]>(`${this.baseUrl}GetAllCompanyByPage`, {
      observe: 'response',
      params,
    });
  }

  getCompanies(): Observable<HttpResponse<Company[]>> {
    return this.http.get<Company[]>(`${this.baseUrl}GetAllCompanyByPage`, {
      observe: 'response',
    });
  }

  deleteCompany(id: string): Observable<HttpResponse<Object>> {
    return this.http
      .delete<Object>(`${this.baseUrl}CompanyDeleteById/${id}`, { observe: 'response' })
      .pipe(
        map((response: HttpResponse<Object>) => response),
        catchError((error) => this.handleError(error))
      );
  }

  updateCompany(companyData: CompanyWithLogo): Observable<HttpResponse<Object>> {
    return this.http
      .patch<Object>(`${this.baseUrl}CompanyUpdate`, companyData, { observe: 'response' })
      .pipe(
        map((response: HttpResponse<Object>) => response),
        catchError((error) => this.handleError(error))
      );
  }

  getCompanyByCode(companyCode: string): Observable<HttpResponse<Company>> {
    return this.http
      .get<Company>(`${this.baseUrl}GetCompanyById/${companyCode}`, {
        observe: 'response',
      })
      .pipe(
        map((response: HttpResponse<Company>) => response),
        catchError((error) => this.handleError(error))
      );
  }

  searchCompanies(
    pageNumber: number,
    pageSize: number,
    searchTerm: string
  ): Observable<HttpResponse<Company[]>> {
    let params = new HttpParams();
    params = params.append('pageNumber', pageNumber);
    params = params.append('pageSize', pageSize);
    params = params.append('search', searchTerm);

    return this.http.get<Company[]>(`${this.baseUrl}GetCompanyListPagedAsync`, {
      observe: 'response',
      params,
    });
  }

  private handleError(
    error: HttpErrorResponse
  ): Observable<HttpResponse<Company>> {
    let errorMessage;

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client-side error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server error (${error.status}): ${error.error.title}`;
    }

    console.error('Error processing Company Data:', errorMessage);

    // Return an Observable with an error HttpResponse
    return throwError(
      () => new HttpResponse<Company>({ status: error.status, body: null })
    );
  }

  private handleErrorWithLogo(
    error: HttpErrorResponse
  ): Observable<HttpResponse<CompanyWithLogo>> {
    let errorMessage;

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client-side error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Server error (${error.status}): ${error.error.title}`;
    }

    console.error('Error processing Company Data:', errorMessage);

    // Return an Observable with an error HttpResponse
    return throwError(
      () => new HttpResponse<CompanyWithLogo>({ status: error.status, body: null })
    );
  }
}
