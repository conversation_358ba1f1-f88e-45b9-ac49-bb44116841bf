import { Injectable } from '@angular/core';
import { MenuItem } from '../../models/shared/menu-item.model';

@Injectable({
  providedIn: 'root',
})
export class SidebarService {
  getMenuItems(): MenuItem[] {
    return [
      {
        label: 'Dashboard',
        icon: 'pi pi-home',
        routerLink: '/admin/',
      },
      {
        label: 'Job Portal',
        icon: 'pi pi-briefcase',
        items: [
          {
            label: 'Job Management',
            icon: 'pi pi-list',
            routerLink: '/admin/job-posting/job-post-management',
          },
          {
            label: 'Job Category',
            icon: 'pi pi-tags',
            routerLink: '/admin/job-posting/job-category-manager',
          },
          {
            label: 'Job Partners',
            icon: 'pi pi-building',
            routerLink: '/admin/company-management/view-companies',
          },
          {
            label: 'Job Applications',
            icon: 'pi pi-file',
            routerLink: '/admin/job-applications',
          },
        ],
      },
      {
        label: 'Training Management',
        icon: 'pi pi-graduation-cap',
        items: [
          {
            label: 'Organization',
            icon: 'pi pi-building',
            items: [
              {
                label: 'Register New',
                icon: 'pi pi-plus',
                routerLink: '/admin/training/training-organization-registration',
              },
              {
                label: 'Approvals',
                icon: 'pi pi-check',
                routerLink: '/admin/training/organization-approvals',
              },
            ]
          },
          {
            label: 'Qualification Types',
            icon: 'pi pi-list',
            routerLink: '/admin/training/qualification-type-management',
          },
          {
            label: 'Professional Bodies',
            icon: 'pi pi-building',
            routerLink: '/admin/training/professional-body-management',
          },
          {
            label: 'Qualifications',
            icon: 'pi pi-check-square',
            routerLink: '/admin/training/qualifications',
          },
        ],
      },
      {
        label: 'Examination',
        icon: 'pi pi-file-o',
        routerLink: '',
      },
      {
        label: 'Exemptions',
        icon: 'pi pi-shield',
        routerLink: '',
      },
      {
        label: 'Education & Training',
        icon: 'pi pi-graduation-cap',
        routerLink: '',
      },
      {
        label: 'Reports',
        icon: 'pi pi-chart-bar',
      },
      {
        label: 'Settings',
        icon: 'pi pi-cog',
        items: [
          {
            label: 'Profile',
            icon: 'pi pi-user',
            routerLink: '/settings/profile',
          },
          {
            label: 'Security',
            icon: 'pi pi-lock',
            routerLink: '/settings/security',
          },
        ],
      },
    ];
  }
}
