import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { QualificationType } from '../../../models/admin/training/qualification-type.model';
import { environment } from '../../../../environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class QualificationTypeService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = `${environment.apiUrl}QualificationType/`;

  createQualificationType(
    qualificationType: Omit<QualificationType, 'qualificationTypeId'>
  ): Observable<HttpResponse<QualificationType>> {
    return this.http.post<QualificationType>(
      `${this.baseUrl}CreateQualificationType`,
      qualificationType,
      { observe: 'response' }
    );
  }

  getAllQualificationTypes(): Observable<QualificationType[]> {
    return this.http.get<QualificationType[]>(`${this.baseUrl}GetAllQualificationTypes`);
  }

  deleteQualificationType(id: string): Observable<HttpResponse<void>> {
    return this.http.delete<void>(`${this.baseUrl}DeleteQualificationType/${id}`, { observe: 'response' });
  }

  getAllQualificationTypesToList(): Observable<QualificationType[]> {
    return this.http.get<QualificationType[]>(`${this.baseUrl}GetAllQualificationTypesToList`);
  }

  getQualificationTypeById(id: string): Observable<QualificationType> {
    return this.http.get<QualificationType>(`${this.baseUrl}GetQualificationTypeById/${id}`);
  }

  updateQualificationType(
    qualificationType: Partial<QualificationType>
  ): Observable<HttpResponse<QualificationType>> {
    return this.http.patch<QualificationType>(
      `${this.baseUrl}UpdateQualificationType`,
      qualificationType,
      { observe: 'response' }
    );
  }
}
