import { Injectable, inject } from '@angular/core';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
  HttpResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  ApprovalResponse,
  OrganizationDetails,
  PendingOrganization,
} from '../../../models/admin/training/training-organization';
import { environment } from '../../../../environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class TrainingOrganizationApprovalService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = `${environment.apiUrl}TrainingOrganization`;

  getTrainingOrganizations(): Observable<any[]> {
    return this.http
      .get<any[]>(`${this.baseUrl}/GetTrainingOrganizations`)
      .pipe(
        map((response: any[]) => response),
        catchError((error) => this.handleError(error))
      );
  }

  getPendingOrganizations(
    pageNumber: number,
    pageSize: number
  ): Observable<HttpResponse<PendingOrganization[]>> {
    let params = new HttpParams();

    if (pageNumber && pageSize) {
      params = params.append('pageNumber', pageNumber);
      params = params.append('pageSize', pageSize);
    }

    return this.http
      .get<PendingOrganization[]>(`${this.baseUrl}/GetTrainingOrganizationsAsyncPendingApprovel`, {
        observe: 'response',
        params,
      })
      .pipe(catchError((error) => this.handleError(error)));
  }

  getOrganizationDetailsById(
    organizationId: string
  ): Observable<OrganizationDetails> {
    return this.http
      .get<OrganizationDetails>(
        `${this.baseUrl}/GetTrainingOrganizationById/${organizationId}`
      )
      .pipe(
        map((response: OrganizationDetails) => response),
        catchError((error) => this.handleError(error))
      );
  }

  approveOrganization(
    organizationId: string,
    approvalData: {
      trainingSchema: string;
      category: string;
      userID: string;
      approveStatus: number;
      approvedRemarks: string;
    }
  ): Observable<HttpResponse<ApprovalResponse>> {
    const requestBody = {
      organizationId,
      trainingSchema: approvalData.trainingSchema,
      category: approvalData.category,
      userID: approvalData.userID,
      approveStatus: approvalData.approveStatus,
      approvedRemarks: approvalData.approvedRemarks
    };

    return this.http
      .patch<ApprovalResponse>(
        `${this.baseUrl}/ApproveTrainingOrganizationAsync/${organizationId}`,
        requestBody,
        { observe: 'response' }
      )
      .pipe(catchError((error) => this.handleError(error)));
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Bad Request: Please check your input data';
          break;
        case 401:
          errorMessage = 'Unauthorized: Please log in again';
          break;
        case 403:
          errorMessage =
            'Forbidden: You do not have permission to perform this action';
          break;
        case 404:
          errorMessage =
            'Not Found: The requested organization was not found';
          break;
        case 409:
          errorMessage =
            'Conflict: Organization approval/rejection conflict occurred';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Please try again later';
          break;
        default:
          errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('Training Organization Approval Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
