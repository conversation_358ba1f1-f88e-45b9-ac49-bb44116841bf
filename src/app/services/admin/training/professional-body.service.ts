import { Injectable, inject } from '@angular/core';
import {
  HttpClient,
  HttpErrorResponse,
  HttpResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ProfessionalBody } from '../../../models/admin/training/professional-body.model';
import { environment } from '../../../../environments/environment.development';

@Injectable({
  providedIn: 'root',
})
export class ProfessionalBodyService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = `${environment.apiUrl}ProfessionalBody`;

  getProfessionalBodyById(id: string): Observable<ProfessionalBody> {
    return this.http
      .get<ProfessionalBody>(`${this.baseUrl}/GetProfessionalBodyById/${id}`)
      .pipe(
        map((response: ProfessionalBody) => response),
        catchError((error) => this.handleError(error))
      );
  }

  getAllProfessionalBodies(): Observable<ProfessionalBody[]> {
    return this.http
      .get<ProfessionalBody[]>(`${this.baseUrl}/GetAllProfessionalBodies`)
      .pipe(
        map((response: ProfessionalBody[]) => response),
        catchError((error) => this.handleError(error))
      );
  }

  getAllProfessionalBodiesToList(): Observable<ProfessionalBody[]> {
    return this.http
      .get<ProfessionalBody[]>(`${this.baseUrl}/GetAllProfessionalBodiesToList`)
      .pipe(
        map((response: ProfessionalBody[]) => response),
        catchError((error) => this.handleError(error))
      );
  }

  createProfessionalBody(
    professionalBody: Omit<ProfessionalBody, 'professionalBodyId'>
  ): Observable<HttpResponse<ProfessionalBody>> {
    return this.http
      .post<ProfessionalBody>(
        `${this.baseUrl}/CreateProfessionalBody`,
        professionalBody,
        { observe: 'response' }
      )
      .pipe(catchError((error) => this.handleError(error)));
  }

  updateProfessionalBody(
    professionalBody: ProfessionalBody
  ): Observable<HttpResponse<ProfessionalBody>> {
    return this.http
      .patch<ProfessionalBody>(
        `${this.baseUrl}/UpdateProfessionalBody`,
        professionalBody,
        { observe: 'response' }
      )
      .pipe(catchError((error) => this.handleError(error)));
  }

  deleteProfessionalBody(id: string): Observable<HttpResponse<void>> {
    return this.http
      .delete<void>(`${this.baseUrl}/DeleteProfessionalBody/${id}`, {
        observe: 'response',
      })
      .pipe(catchError((error) => this.handleError(error)));
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Bad Request: Please check your input data';
          break;
        case 401:
          errorMessage = 'Unauthorized: Please log in again';
          break;
        case 403:
          errorMessage =
            'Forbidden: You do not have permission to perform this action';
          break;
        case 404:
          errorMessage =
            'Not Found: The requested professional body was not found';
          break;
        case 409:
          errorMessage =
            'Conflict: A professional body with this name already exists';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Please try again later';
          break;
        default:
          errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('Professional Body Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
