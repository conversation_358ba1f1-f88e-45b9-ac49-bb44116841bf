import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Qualification, CreateQualification } from '../../../models/admin/training/qualification.model';
import { environment } from '../../../../environments/environment.development';

@Injectable({
  providedIn: 'root'
})
export class QualificationService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = `${environment.apiUrl}Qualification`;

  /**
   * Get qualification by ID
   * @param id - Qualification ID
   * @returns Observable with qualification details
   */
  getQualificationById(id: string): Observable<Qualification> {
    return this.http
      .get<Qualification>(`${this.baseUrl}/GetQualificationById/${id}`)
      .pipe(
        map((response: Qualification) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Get all qualifications
   * @returns Observable with list of qualifications
   */
  getAllQualifications(): Observable<Qualification[]> {
    return this.http
      .get<Qualification[]>(`${this.baseUrl}/GetAllQualifications`)
      .pipe(
        map((response: Qualification[]) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Get all qualifications for list/dropdown purposes
   * @returns Observable with list of qualifications
   */
  getAllQualificationsToList(): Observable<Qualification[]> {
    return this.http
      .get<Qualification[]>(`${this.baseUrl}/GetAllQualificationsToList`)
      .pipe(
        map((response: Qualification[]) => response),
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Create a new qualification
   * @param qualification - Qualification data to create
   * @returns Observable with HTTP response
   */
  createQualification(
    qualification: CreateQualification
  ): Observable<HttpResponse<Qualification>> {
    return this.http
      .post<Qualification>(`${this.baseUrl}/CreateQualification`, qualification, { observe: 'response' })
      .pipe(
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Update an existing qualification
   * @param qualification - Qualification data to update
   * @returns Observable with HTTP response
   */
  updateQualification(
    qualification: Qualification
  ): Observable<HttpResponse<Qualification>> {
    return this.http
      .patch<Qualification>(`${this.baseUrl}/UpdateQualification`, qualification, { observe: 'response' })
      .pipe(
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Delete a qualification
   * @param id - Qualification ID to delete
   * @returns Observable with HTTP response
   */
  deleteQualification(id: string): Observable<HttpResponse<void>> {
    return this.http
      .delete<void>(`${this.baseUrl}/DeleteQualification/${id}`, { observe: 'response' })
      .pipe(
        catchError((error) => this.handleError(error))
      );
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   * @returns Observable error
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Bad Request: Please check your input data';
          break;
        case 401:
          errorMessage = 'Unauthorized: Please log in again';
          break;
        case 403:
          errorMessage = 'Forbidden: You do not have permission to perform this action';
          break;
        case 404:
          errorMessage = 'Not Found: The requested qualification was not found';
          break;
        case 409:
          errorMessage = 'Conflict: A qualification with this name already exists';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Please try again later';
          break;
        default:
          errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('Qualification Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
