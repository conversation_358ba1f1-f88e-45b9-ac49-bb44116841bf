import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';
import { UserRole } from '../../models/auth/auth';

export const roleGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  const requiredRoles = route.data['roles'] as Array<UserRole>;

  if (!requiredRoles || requiredRoles.length === 0) {
    return true;
  }

  const currentUser = authService.getCurrentUser();

  if (currentUser && currentUser.roles && requiredRoles.some(role => currentUser.roles.includes(role))) {
    return true;
  } else {
    router.navigate(['/']);
    return false;
  }
};
