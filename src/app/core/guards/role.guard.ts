import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';
import { UserRole } from '../../models/auth/auth';

export const roleGuard: CanActivateFn = (route, _state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  const requiredRoles = route.data['roles'] as Array<UserRole>;

  // If no roles are required, allow access
  if (!requiredRoles || requiredRoles.length === 0) {
    return true;
  }

  const currentUser = authService.getCurrentUser();

  // Check if user exists and has roles
  if (!currentUser) {
    router.navigate(['/']);
    return false;
  }

  if (!currentUser.roles || currentUser.roles.length === 0) {
    router.navigate(['/']);
    return false;
  }

  // Check if user has any of the required roles
  const hasRequiredRole = requiredRoles.some(role => currentUser.roles.includes(role));

  if (hasRequiredRole) {
    return true;
  } else {
    router.navigate(['/']);
    return false;
  }
};
