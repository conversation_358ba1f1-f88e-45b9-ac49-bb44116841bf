import { HttpInterceptorFn } from '@angular/common/http';
import { AuthService } from '../../services/auth/auth.service';
import { inject } from '@angular/core';

export const jwtInterceptor: HttpInterceptorFn = (req, next) => {
  const accountService = inject(AuthService);
  if (accountService.getCurrentUser()) {
      req = req.clone({
          setHeaders: {
              Authorization: `Bearer ${accountService.getToken()}`
          }
      })
  }
  return next(req);
};
