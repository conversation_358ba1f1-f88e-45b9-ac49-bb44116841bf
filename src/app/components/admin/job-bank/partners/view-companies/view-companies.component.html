<p-toast></p-toast>
<p-confirmdialog acceptButtonStyleClass="p-button-warn" rejectButtonStyleClass="p-button-secondary" />


<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-3xl">Partner Management</span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"
                styleClass="custom-breadcrumb"></p-breadcrumb>
        </div>
    </div>
</div>

<p-toolbar styleClass="mb-6 mx-4 py-4 md:p-3">
    <ng-template #start>
      <p-button label="Add New Partner" icon="pi pi-plus" severity="primary" class="mr-2"
        (onClick)="navigateToAddCompany()"></p-button>
    </ng-template>
    <ng-template #end>
      <p-iconfield>
        <p-inputicon styleClass="pi pi-search"></p-inputicon>
        <input pInputText type="text" placeholder="Search..." [formControl]="searchControl" 
               (input)="onSearchChange($event)" />
      </p-iconfield>
    </ng-template>
  </p-toolbar>

<div class="shadow-md mx-4 py-4 md:p-3 rounded-lg bg-white">
    <div class="card">
        <p-table [value]="companies" [tableStyle]="{ 'min-width': '50rem' }">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width:5%" scope="col">No</th>
                    <th style="width:10%" scope="col">Company Name</th>
                    <th style="width:10%" scope="col">Contact Person</th>
                    <th style="width:10%" scope="col">Email Address</th>
                    <th style="width:10%" scope="col">Business Type</th>
                    <th style="width:7%" scope="col">Mobile</th>
                    <th style="width:7%" scope="col">WhatsApp</th>
                    <th style="width:5%" scope="col">Actions</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-company let-i="rowIndex">
                <tr>
                    <td>{{ i + 1 }}</td>
                    <td>{{ company.companyName }}</td>
                    <td>{{ company.contactPerson }}</td>
                    <td>{{ company.emailAddress }}</td>
                    <td>{{ company.businessType }}</td>
                    <td>{{ company.contactMobile }}</td>
                    <td>{{ company.contactWhatsApp }}</td>
                    <td>
                        <div class="flex gap-2">
                            <button pButton type="button" icon="pi pi-eye"
                                class="p-button-rounded p-button-info p-button-text"
                                (click)="viewCompanyDetails(company)">
                            </button>
                            <button pButton type="button" icon="pi pi-pencil"
                                class="p-button-rounded p-button-success p-button-text"
                                (click)="updateCompany(company.companyCode)">
                            </button>
                            <button pButton type="button" icon="pi pi-trash"
                                class="p-button-rounded p-button-danger p-button-text"
                                (click)="deleteCompanyById(company.companyCode)">
                            </button>
                        </div>
                    </td>
                </tr>
            </ng-template>
        </p-table>

        <p-paginator [rows]="pageSize" [totalRecords]="totalRecords" [showCurrentPageReport]="true"
            [rowsPerPageOptions]="[10, 15, 20, 50]"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
            (onPageChange)="onPageChange($event)"></p-paginator>
    </div>
</div>

<p-dialog [(visible)]="showCompanyDetailsDialog" [style]="{width: '700px'}"[modal]="true"
    styleClass="p-fluid" [draggable]="false" [resizable]="false">
    <div *ngIf="selectedCompany" class="p-4">
        <!-- First row: Logo and basic company info -->
        <div class="mb-4 grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Company Logo Section -->
            <div class="col-span-1">
                <div class="border-1 border-300 flex align-items-center justify-content-center"
                    style="height: 150px; width: 150px;">
                    <p-image *ngIf="selectedCompany.logoUrl" [src]="selectedCompany.logoUrl" alt="Company Logo"
                        width="140" height="140" [preview]="true">
                    </p-image>
                    <div *ngIf="!selectedCompany.logoUrl" class="flex align-items-center justify-content-center"
                        style="height: 140px; width: 140px;">
                        <span class="text-xl font-bold">Logo</span>
                    </div>
                </div>
            </div>

            <!-- Company Basic Info Section -->
            <div class="col-span-2">
                <div class="text-2xl font-bold">{{selectedCompany.companyName}}</div>
                <div class="text-lg text-500 mb-2">{{selectedCompany.moto}}</div>
                <p-tag severity="info" value= {{selectedCompany.businessType}} [rounded]="true" />
            </div>
        </div>

        <!-- Second row: Company description -->
        <div class="border-1 border-300 p-3 mb-4">
            <div class="text-center mb-2">{{selectedCompany.description || 'Company Description'}}</div>
        </div>

        <!-- Third row: Company detailed information -->
        <div class="mb-4">
            <p-table [value]="[
            { label: 'Address', value: selectedCompany.address || 'N/A' },
            { label: 'Email Address', value: selectedCompany.emailAddress || 'N/A' },
            { label: 'Fixed line number', value: selectedCompany.contactFixed || 'N/A' },
            { label: 'WhatsApp number', value: selectedCompany.contactWhatsApp || 'N/A' },
            { label: 'Mobile number', value: selectedCompany.contactMobile || 'N/A' },
            { label: 'Contact Person', value: selectedCompany.contactPerson || 'N/A' }
            ]" [stripedRows]="true" styleClass="w-full rounded-lg overflow-hidden">
            <ng-template pTemplate="body" let-item>
                <tr>
                <td class="text-left p-2 bg-gray-100 w-1/3 font-bold">{{item.label}}:</td>
                <td class="p-2">{{item.value}}</td>
                </tr>
            </ng-template>
            </p-table>
        </div>
    </div>
</p-dialog>