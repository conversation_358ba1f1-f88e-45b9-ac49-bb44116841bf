import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { TagModule } from 'primeng/tag';
import { PaginatorModule } from 'primeng/paginator';
import { DialogModule } from 'primeng/dialog';
import { CompanyManagementService } from '../../../../../services/admin/job-bank/company-management.service';
import { HttpResponse } from '@angular/common/http';
import { Company } from '../../../../../models/admin/job-bank/company';
import { Subject, takeUntil } from 'rxjs';
import { Router } from '@angular/router';
import { ImageModule } from 'primeng/image';
import { DividerModule } from 'primeng/divider';
import { ToolbarModule } from 'primeng/toolbar';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-view-companies',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    ToastModule,
    ButtonModule,
    ConfirmDialogModule,
    PaginatorModule,
    DialogModule,
    ImageModule,
    DividerModule,
    TagModule,
    InputIconModule,
    IconFieldModule,
    InputTextModule,
    ToolbarModule,
    BreadcrumbModule,
    ReactiveFormsModule,
  ],
  templateUrl: './view-companies.component.html',
  styleUrls: ['./view-companies.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class ViewCompaniesComponent implements OnInit {
  private readonly companyService = inject(CompanyManagementService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();
  private readonly searchTerms = new Subject<string>();

  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
  companies: Company[] = [];
  selectedCompany: Company | null = null;
  showCompanyDetailsDialog: boolean = false;
  searchControl: FormControl = new FormControl('');

  totalRecords: number = 0;
  pageSize: number = 10;
  pageNumber: number = 1;

  ngOnInit(): void {
    this.getCompanies(this.pageNumber, this.pageSize);
    this.initializeBreadcrumb();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/admin' };
    this.breadcrumbItems = [{ label: 'Partner Managemant' }];
  }

  private setupSearch(): void {
    this.searchTerms
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(1000),
        distinctUntilChanged()
      )
      .subscribe((searchTerm) => {
        this.pageNumber = 1;
        this.searchCompanies(searchTerm);
      });
  }

  onSearchChange(event: Event): void {
    const searchTerm = this.searchControl.value;
    this.searchTerms.next(searchTerm);
  }

  private searchCompanies(searchTerm: string): void {
    this.companyService
      .searchCompanies(this.pageNumber, this.pageSize, searchTerm)
      .subscribe({
        next: (response: HttpResponse<Company[]>) => {
          if (response.body) {
            this.companies = response.body;
          }
          const paginationHeader: string | null =
            response.headers.get('Pagination');
          if (paginationHeader) {
            const pagination = JSON.parse(paginationHeader);
            this.totalRecords = pagination.totalItems;
          }
        },
        error: () => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to search companies',
          });
        },
      });
  }

  onPageChange(event: any): void {
    this.pageNumber = Math.floor(event.first / event.rows) + 1;
    this.pageSize = event.rows;

    if (this.searchControl.value && this.searchControl.value.trim() !== '') {
      this.searchCompanies(this.searchControl.value);
    } else {
      this.getCompanies(this.pageNumber, this.pageSize);
    }
  }

  getCompanies(pageNumber: number, pageSize: number): void {
    this.companyService.getCompaniesPaged(pageNumber, pageSize).subscribe({
      next: (response: HttpResponse<Company[]>) => {
        if (response.body) {
          this.companies = response.body;
        }
        const paginationHeader: string | null =
          response.headers.get('Pagination');
        if (paginationHeader) {
          const pagination = JSON.parse(paginationHeader);
          this.totalRecords = pagination.totalItems;
        }
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load companies',
        });
      },
    });
  }

  viewCompanyDetails(company: Company): void {
    this.selectedCompany = company;
    this.showCompanyDetailsDialog = true;
  }

  deleteCompanyById(companyCode: string): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this company?',
      header: 'Delete Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.companyService
          .deleteCompany(companyCode)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Company deleted successfully',
              });
              this.getCompanies(this.pageNumber, this.pageSize);
            },
            error: (error) => {
              console.error('Error deleting company:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete company',
              });
            },
          });
      },
    });
  }

  updateCompany(companyCode: string): void {
    if (!companyCode || companyCode.trim() === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Company code is required',
      });
      return;
    }

    this.router
      .navigate(['/admin/company-managemant/company-registration'], {
        queryParams: { companyCode: companyCode, updateMode: true },
      })
      .catch((error) => {
        console.error('Navigation error:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to navigate to update page',
        });
      });
  }

  navigateToAddCompany() {
    this.router.navigate(['/admin/company-managemant/company-registration']);
  }
}
