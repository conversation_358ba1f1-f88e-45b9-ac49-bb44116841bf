import { Component, OnInit, inject } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Subject, takeUntil } from 'rxjs';
import { CompanyManagementService } from '../../../../../services/admin/job-bank/company-management.service';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { FileUploadModule } from 'primeng/fileupload';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { CompanyWithLogo } from '../../../../../models/admin/job-bank/company';

@Component({
  selector: 'app-company-registration',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputTextModule,
    ButtonModule,
    ToastModule,
    FileUploadModule,
    ConfirmDialogModule,
    BreadcrumbModule,
  ],
  templateUrl: './company-registration.component.html',
  styleUrls: ['./company-registration.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class CompanyRegistrationComponent implements OnInit {
  private readonly fb = inject(FormBuilder);
  private readonly messageService = inject(MessageService);
  private readonly companyService = inject(CompanyManagementService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly sanitizer = inject(DomSanitizer);
  private readonly destroy$ = new Subject<void>();

  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
  companyForm!: FormGroup;
  logoFile: File | null = null;
  isUpdateMode: boolean = false;
  isLogoUpdate: boolean = false;
  selectedCompanyCode: string = '';
  selectedcompanylogoUrl: string = '';
  previewImage: SafeUrl | null = null;
  companyId: string = '';

  ngOnInit(): void {
    this.initializeForm();
    this.handleQueryParams();
    this.initializeBreadcrumb();
  }

  private initializeForm(): void {
    this.companyForm = this.fb.group({
      companyName: ['', [Validators.required]],
      address: ['', [Validators.required]],
      contactMobile: ['', [Validators.required]],
      contactWhatsApp: ['', [Validators.required]],
      contactFixed: [''],
      contactPerson: ['', [Validators.required]],
      emailAddress: ['', [Validators.required, Validators.email]],
      businessType: ['', [Validators.required]],
      moto: [''],
      description: ['', [Validators.required, Validators.maxLength(200)]],
    });
  }

  private handleQueryParams(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        if (params['updateMode'] === 'true' && params['companyCode']) {
          this.isUpdateMode = true;
          this.selectedCompanyCode = params['companyCode'];
          this.loadCompanyData(this.selectedCompanyCode);
        }
      });
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/admin' };
    this.breadcrumbItems = [
      {
        label: 'Company Managemant',
        routerLink: '/admin/company-managemant/company-registration',
      },
      { label: 'Company Registration' },
    ];
  }

  onLogoUpload(event: any): void {
    if (event.files && event.files.length > 0) {
      const file = event.files[0];

      if (this.isUpdateMode && file.size > 0) {
        this.isLogoUpdate = true;
      }

      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        this.messageService.add({
          severity: 'error',
          summary: 'Invalid File',
          detail: 'Please upload only image files (JPEG, PNG)',
        });
        return;
      }

      if (file.size > 1048576) {
        this.messageService.add({
          severity: 'error',
          summary: 'File Too Large',
          detail: 'Image size should be less than 1MB',
        });
        return;
      }

      this.logoFile = file;

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          this.previewImage = this.sanitizer.bypassSecurityTrustUrl(
            e.target.result as string
          );
        }
      };
      reader.readAsDataURL(file);

      this.messageService.add({
        severity: 'info',
        summary: 'File Selected',
        detail: 'Logo successfully selected',
      });
    }
  }

  addNewCompany(): void {
    if (this.companyForm.valid) {
      if (this.logoFile) {
        this.convertLogoToBase64(this.logoFile, (logoBase64, fileName) => {
          const companyData = this.createCompanyDataObject(logoBase64, fileName);
          this.sendCompanyData(companyData);
        });
      } else {
        const companyData = this.createCompanyDataObject('', '');
        this.sendCompanyData(companyData);
      }
    }
  }

  updateExistingCompany(): void {
    if (this.companyForm.valid) {
      this.confirmationService.confirm({
        message: 'Are you sure you want to update this company?',
        header: 'Update Confirmation',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          if (this.logoFile) {
            this.convertLogoToBase64(this.logoFile, (logoBase64, fileName) => {
              const companyData = this.createCompanyDataObject(logoBase64, fileName);
              // Add company code for update
                const updateData = {
                  ...companyData,
                  companyCode: this.selectedCompanyCode,
                  isLogoChanged: this.isLogoUpdate
                };
              this.sendUpdateCompanyData(updateData);
            });
          } else {
            const companyData = this.createCompanyDataObject('Test', 'data.jpeg');
            // Add company code for update
            const updateData = {
              ...companyData,
              companyCode: this.selectedCompanyCode,
              isLogoChanged: this.isLogoUpdate,
            };
            this.sendUpdateCompanyData(updateData);
          }
        },
      });
    }
  }

  loadCompanyData(companyCode: string): void {
    this.companyService
      .getCompanyByCode(companyCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const companyData = response.body;
          if (companyData) {
            this.companyForm.patchValue({
              companyName: companyData.companyName,
              address: companyData.address,
              contactMobile: companyData.contactMobile,
              contactWhatsApp: companyData.contactWhatsApp,
              contactFixed: companyData.contactFixed,
              contactPerson: companyData.contactPerson,
              emailAddress: companyData.emailAddress,
              businessType: companyData.businessType,
              moto: companyData.moto,
              description: companyData.description,
            });

            if (companyData.logoUrl) {
              this.previewImage = companyData.logoUrl;
            }
            this.selectedcompanylogoUrl = companyData.logoUrl;
          }
        },
        error: (error: unknown) => {
          console.error('Error loading company data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load company data',
          });
        },
      });
  }

  private convertLogoToBase64(file: File, callback: (logoBase64: string, fileName: string) => void): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      // Get base64 string by removing data:image/xxx;base64, prefix
      const base64String = reader.result as string;
      const base64Content = base64String.split(',')[1];
      callback(base64Content, file.name);
    };
  }

  private createCompanyDataObject(logoBase64: string, fileName: string): CompanyWithLogo {
    return {
      companyName: this.companyForm.value.companyName,
      address: this.companyForm.value.address,
      contactMobile: this.companyForm.value.contactMobile,
      contactWhatsApp: this.companyForm.value.contactWhatsApp,
      contactFixed: this.companyForm.value.contactFixed,
      contactPerson: this.companyForm.value.contactPerson,
      emailAddress: this.companyForm.value.emailAddress,
      logoBase64: logoBase64,
      fileName: fileName,
      description: this.companyForm.value.description,
      moto: this.companyForm.value.moto,
      businessType: this.companyForm.value.businessType,
      isLogoChanged: this.isLogoUpdate,
      companyCode: this.selectedCompanyCode,
      logoUrl: this.selectedcompanylogoUrl,
    };
  }

  private sendCompanyData(companyData: CompanyWithLogo): void {
    this.companyService.addCompany(companyData).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Company added successfully',
        });
        this.companyForm.reset();
        this.previewImage = null;
        this.logoFile = null;
        setTimeout(() => {
          this.router.navigate(['/admin/company-managemant/view-companies']);
        }, 500);
      },
      error: (error) => {
        console.error('Error creating company:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to create new company',
        });
      },
    });
  }

  private sendUpdateCompanyData(companyData: CompanyWithLogo & { companyCode: string }): void {
    this.companyService.updateCompany(companyData).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Company updated successfully',
        });
        this.companyForm.reset();
        setTimeout(() => {
          this.router.navigate(['/admin/company-managemant/view-companies']);
        }, 500);
      },
      error: (error) => {
        console.error('Error updating company:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update company',
        });
      },
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  cancelUpdate(): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to cancel the update?',
      header: 'Cancel Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.router.navigate(['/admin/company-managemant/view-companies']);
      },
    });
  }
}
