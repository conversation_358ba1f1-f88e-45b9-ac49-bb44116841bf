<p-toast />
<p-confirmdialog />

<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-3xl">{{ isUpdateMode ? 'Company Update' : 'Company Registration' }}</span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"
                styleClass="custom-breadcrumb"></p-breadcrumb>
        </div>
    </div>
</div>

<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <form [formGroup]="companyForm" (ngSubmit)="isUpdateMode ? updateExistingCompany() : addNewCompany()">

        <!-- Company Name and Logo in same row -->
        <div class="mb-4 grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Left Column (2/3) -->
            <div class="col-span-1 md:col-span-2 flex flex-col">
                <!-- Company Name Row -->
                <div class="mb-4">
                    <label class="block font-semibold mb-2">Company Name</label>
                    <input pInputText id="companyName" formControlName="companyName" autocomplete="off" class="w-full"
                        placeholder="Enter company name" />
                </div>

                <!-- Company Description Row -->
                <div>
                    <label class="block font-semibold mb-2">Company Description (Max 200 characters)</label>
                    <textarea pTextarea formControlName="description"
                        class="w-full p-2 border border-gray-300 rounded-lg" rows="3" maxlength="200"
                        placeholder="Enter a brief company description"></textarea>
                    <small class="text-gray-500">{{ (companyForm.get('description')?.value?.length || 0) }}/200
                        characters</small>
                </div>
            </div>

            <!-- Right Column (1/3) -->
            <div class="col-span-1">
                <label class="block font-semibold mb-2">Company Logo</label>
                <div class="flex flex-col items-center">
                    <div
                        class="w-32 h-32 border border-gray-300 rounded-lg flex items-center justify-center mb-2 bg-gray-50 overflow-hidden">
                        <img *ngIf="previewImage" [src]="previewImage" alt="Company Logo"
                            class="max-w-full max-h-full object-contain" />
                    </div>
                    <p-fileUpload #fileUpload mode="basic" chooseLabel="Select Logo" accept="image/*"
                        [maxFileSize]="1000000" [auto]="false" (onSelect)="onLogoUpload($event)"
                        styleClass="p-button-sm p-button-outlined">
                    </p-fileUpload>
                    <small class="text-gray-500 text-center mt-1">Recommended: 200x200px, max 1MB</small>
                </div>
            </div>
        </div>

        <div class="mb-4 flex flex-col md:flex-row gap-6">
            <!-- Address -->
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Address</label>
                <input pInputText formControlName="address" class="w-full p-2 border border-gray-300 rounded-lg"
                    rows="3" placeholder="Enter company address" />
            </div>
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Fixed Line Number</label>
                <input pInputText id="fixedNumber" autocomplete="off" formControlName="contactFixed" class="w-full"
                    placeholder="Enter fixed line number" />
            </div>
        </div>

        <!-- Contact Numbers in same row -->
        <div class="mb-4 flex flex-col md:flex-row gap-6">
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Mobile Number</label>
                <input pInputText id="mobileNumber" autocomplete="off" formControlName="contactMobile" class="w-full"
                    placeholder="Enter mobile number" />
            </div>
            <!-- Whatsapp Number -->
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Whatsapp Number</label>
                <input pInputText id="contactWhatsApp" autocomplete="off" formControlName="contactWhatsApp"
                    class="w-full" placeholder="Enter Whatsapp number" />
            </div>
        </div>

        <!-- Contact Person and Email in same row -->
        <div class="mb-4 flex flex-col md:flex-row gap-6">
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Contact Person</label>
                <input pInputText id="contactPerson" autocomplete="off" formControlName="contactPerson" class="w-full"
                    placeholder="Enter contact person name" />
            </div>
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Email Address</label>
                <input pInputText id="email" autocomplete="off" formControlName="emailAddress" class="w-full"
                    placeholder="Enter email address" />
            </div>
        </div>

        <!-- Business Type and Company Motto in same row -->
        <div class="mb-4 flex flex-col md:flex-row gap-6">
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Business Type</label>
                <input pInputText id="businessType" autocomplete="off" formControlName="businessType" class="w-full"
                    placeholder="Enter business type" />
            </div>
            <div class="w-full md:w-1/2">
                <label class="block font-semibold mb-2">Company Motto</label>
                <input pInputText id="companyMotto" autocomplete="off" formControlName="moto" class="w-full"
                    placeholder="Enter company motto" />
            </div>
        </div>

        <!-- Submit and Cancel Buttons -->
        <div class="flex justify-end gap-3 mt-10">
            <p-button *ngIf="isUpdateMode" type="button" icon="pi pi-times" label="Cancel"
                styleClass="p-button-outlined p-button-secondary" (onClick)="cancelUpdate()"></p-button>
            <p-button type="submit" icon="pi pi-check" [label]="isUpdateMode ? 'Update' : 'Register'"
                [disabled]="companyForm.invalid"></p-button>
        </div>
    </form>
</div>