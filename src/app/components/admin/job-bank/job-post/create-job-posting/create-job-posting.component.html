<p-toast></p-toast>
<p-confirmdialog acceptButtonStyleClass="p-button-warn" rejectButtonStyleClass="p-button-secondary" />

<div class="w-full px-4 py-4">
  <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
      <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
          <span class="text-white font-semibold text-3xl">{{ isUpdateMode ? 'Update Job Post' : 'Creat Job Post' }}</span>
          <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
      </div>
  </div>
</div>

<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
  <form [formGroup]="jobForm" (ngSubmit)="isUpdateMode ? updateJobPost() : addJobPost()">

    <!-- Job Title -->
    <div class="mb-4">
      <label class="block font-semibold mb-2">Job Title</label>
      <input pInputText id="jobTitle" formControlName="jobTitle" autocomplete="off" class="w-full"
        placeholder="Enter job title" maxlength="30" />
      <small *ngIf="jobForm.get('jobTitle')?.value?.length >= 30" class="text-red-500 mt-1 block">
        Job title should be less than 30 characters
      </small>
    </div>

    <!-- Job Summary -->
    <div class="mb-4">
      <label class="block font-semibold mb-2">Job Summary</label>
      <input pInputText id="jobSummary" formControlName="jobSummary" autocomplete="off" class="w-full"
        placeholder="Enter job summary" maxlength="50" />
      <small *ngIf="jobForm.get('jobSummary')?.value?.length >= 100" class="text-red-500 mt-1 block">
        Job summary should be less than 50 characters
      </small>
    </div>

    <!-- Job Description -->
    <div class="mb-4">
      <label class="block font-semibold mb-2">Job Description</label>
      <textarea pTextarea formControlName="jobDescription" class="w-full p-2" rows="5"
        placeholder="Enter job description"></textarea>
    </div>

    <!-- Company Name and Location in same row -->
    <div class="mb-4 flex flex-col md:flex-row gap-6">
      <div class="w-full md:w-1/2">
        <label class="block font-semibold mb-2">Company Name</label>
        <p-autoComplete formControlName="companyName" [suggestions]="filteredCompanies"
          (completeMethod)="filterCompanies($event)" (onSelect)="onCompanySelect($event)" field="companyName"
          [dropdown]="true" placeholder="Search company name" [style]="{'width':'100%'}" [inputStyle]="{'width':'100%'}"
          [autoHighlight]="true"></p-autoComplete>
      </div>
      <div class="w-full md:w-1/2">
        <label class="block font-semibold mb-2">Company Email</label>
        <input pInputText id="companyEmail" autocomplete="off" formControlName="companyEmail" class="w-full"
          placeholder="Enter company Email" />
      </div>
    </div>

    <!-- Employment Type and Valid Until Date in same row -->
    <div class="mb-4 flex flex-col md:flex-row gap-6">
      <div class="w-full md:w-1/2">
        <label class="block font-semibold mb-2">Location</label>
        <input pInputText id="companyLocation" autocomplete="off" formControlName="location" class="w-full"
          placeholder="Enter company location" />
      </div>
      <div class="w-full md:w-1/2">
        <label class="block font-semibold mb-2">Employment Type</label>
        <p-select [options]="employmentTypes" optionLabel="name" placeholder="Select employment type" class="w-full"
          formControlName="employmentTypeId">
        </p-select>
      </div>
    </div>

    <div class="mb-4 flex flex-col md:flex-row gap-6">
      <div class="w-full md:w-1/2">
        <label class="block font-semibold mb-2">Job Category</label>
        <div class="flex gap-2">
          <p-select [options]="categories" optionLabel="jobCategoryName" formControlName="category"
            placeholder="Select a category" class="w-full">
          </p-select>
          <p-button icon="pi pi-plus" label="Add" severity="primary" (onClick)="showAddCategoryDialog()"
            pTooltip="Add New Category" tooltipPosition="top"></p-button>
        </div>
      </div>
      <div class="w-full md:w-1/2">
        <label class="block font-semibold mb-2">Valid Until</label>
        <p-date-picker [showIcon]="true" dateFormat="dd/mm/yy" [minDate]="minDate" placeholder="Select expiration date"
          styleClass="w-full" [style]="{ width: '100%' }" formControlName="validUntil">
        </p-date-picker>
      </div>
    </div>

    <!-- Job Banner Card -->
    <div class="mb-4 w-full">
      <label for="jobBanner" class="block font-semibold">Job Post Banner</label>
      <p-card Class="w-full">
      <div id="jobBanner" class="flex flex-col items-center p-4">
        <p-image *ngIf="previewImage" [src]="previewImage" [preview]="true" alt="Image" width="250" class="mb-3">
        <ng-template #indicator>
          <i class="pi pi-search"></i>
        </ng-template>
        </p-image>
        
        <div *ngIf="!previewImage" class="border-dashed border-2 border-gray-300 p-6 mb-3 rounded flex items-center justify-center">
        <i class="pi pi-image text-gray-400 text-4xl"></i>
        </div>
        
        <p-fileUpload #fileUpload mode="basic" chooseLabel="Select Banner" accept="image/*" [maxFileSize]="5000000"
        [auto]="false" (onSelect)="onBannerUpload($event)" styleClass="p-button-sm w-full">
        </p-fileUpload>
        <small class="text-gray-500 text-center mt-2">max 5MB</small>
      </div>
      </p-card>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end gap-3 mt-10">
      <p-button *ngIf="isUpdateMode" type="button" icon="pi pi-times" label="Cancel" severity="contrast"
        (onClick)="cancelUpdate()"></p-button>
      <p-button type="submit" [icon]="isUpdateMode ? 'pi pi-refresh' : 'pi pi-check'"
        [label]="isUpdateMode ? 'Update' : 'Submit'" [disabled]="jobForm.invalid"></p-button>
    </div>
  </form>
</div>

<!-- Add Category Dialog -->
<p-dialog header="Add New Job Category" [(visible)]="displayCategoryDialog" [modal]="true" [style]="{width: '30rem'}"
  [closable]="true">
  <form [formGroup]="jobCategoryForm" (ngSubmit)="addNewJobCategory()">
    <div class="p-fluid formgrid grid">
      <div class="field col-12">
        <label for="jobCategoryName" class="font-semibold mb-1">Job Category Name</label>
        <input pInputText id="jobCategoryName" formControlName="jobCategoryName" class="p-inputtext-sm mt-2 mb-3"
          [style]="{width: '100%', border: '1px solid #ced4da'}" placeholder="Enter the job category" />
        <small
          *ngIf="jobCategoryForm.get('jobCategoryName')?.touched && jobCategoryForm.get('jobCategoryName')?.errors?.['required']"
          class="p-error block mt-1 text-red-500">
          Category name is required.
        </small>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="col-12 flex justify-content-end gap-3 mt-4">
      <p-button label="Cancel" severity="secondary" icon="pi pi-times" class="p-button-text"
        (onClick)="displayCategoryDialog = false"></p-button>
      <p-button label="Save" type="submit" icon="pi pi-check" severity="success"
        [disabled]="jobCategoryForm.invalid"></p-button>
    </div>
  </form>
</p-dialog>