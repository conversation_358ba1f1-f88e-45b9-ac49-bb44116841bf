import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { InputTextModule } from 'primeng/inputtext';
import { EditorModule } from 'primeng/editor';
import { FileUploadModule } from 'primeng/fileupload';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { JobCategoryService } from '../../../../../services/admin/job-bank/job-category.service';
import { DatePickerModule } from 'primeng/datepicker';
import { SelectModule } from 'primeng/select';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { JobBankService } from '../../../../../services/shared/job-bank.service';
import { EmploymentType } from '../../../../../models/shared/employment-type';
import { EmploymentTypeService } from '../../../../../services/shared/employment-type.service';
import { JobCategory } from '../../../../../models/shared/job-bank/job-category';
import { CompanyManagementService } from '../../../../../services/admin/job-bank/company-management.service';
import { Company } from '../../../../../models/admin/job-bank/company';
import { HttpResponse } from '@angular/common/http';
import { DialogModule } from 'primeng/dialog';
import { TooltipModule } from 'primeng/tooltip';
import { Subject, takeUntil } from 'rxjs';
import { CommonModule } from '@angular/common';
import { TextareaModule } from 'primeng/textarea';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { ImageModule } from 'primeng/image';
import { CardModule } from 'primeng/card';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { JobBank, JobBankUpdate } from '../../../../../models/shared/job-bank/job-bank';

@Component({
  selector: 'app-create-job-posting',
  standalone: true,
  imports: [
    InputTextModule,
    EditorModule,
    FileUploadModule,
    ButtonModule,
    ReactiveFormsModule,
    SelectModule,
    DatePickerModule,
    DropdownModule,
    FileUploadModule,
    ToastModule,
    AutoCompleteModule,
    DialogModule,
    TooltipModule,
    CommonModule,
    TextareaModule,
    ImageModule,
    CardModule,
    ConfirmDialogModule,
    BreadcrumbModule,
  ],
  templateUrl: './create-job-posting.component.html',
  styleUrls: ['./create-job-posting.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class CreateJobPostingComponent implements OnInit, OnDestroy {
  private readonly jobCategoryService = inject(JobCategoryService);
  private readonly jobBankService = inject(JobBankService);
  private readonly employmentTypeService = inject(EmploymentTypeService);
  private readonly companyService = inject(CompanyManagementService);
  private readonly fb = inject(FormBuilder);
  private readonly messageService = inject(MessageService);
  private readonly route = inject(ActivatedRoute);
  private readonly destroy$ = new Subject<void>();
  private readonly router = inject(Router);
  private readonly sanitizer = inject(DomSanitizer);

  isUpdateMode: boolean = false;
  isJobBannerUpdate: boolean = false;
  selectedBannerUrl: string = '';
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
  jobForm!: FormGroup;
  jobCategoryForm!: FormGroup;
  categories: JobCategory[] = [];
  minDate: Date = new Date();
  employmentTypes: EmploymentType[] = [];
  companies: any[] = []; // List of all companies
  filteredCompanies: Object[] = [];
  selectedCompany: any;
  updateJobPostId: string = '';
  updateJobPostRowVersion: string = '';
  displayCategoryDialog: boolean = false;
  previewImage: SafeUrl | null = null;
  jobPostBanner: File | null = null;
  ngOnInit(): void {
    this.initializeForm();
    this.loadEmploymentTypes();
    this.loadJobCategory();
    this.loadCompanies();
    this.loadUpdatePostDetails();
    this.initCategoryForm();
    this.initializeBreadcrumb();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initializeForm(): void {
    this.jobForm = this.fb.group({
      jobTitle: ['', Validators.required],
      jobSummary: ['', [Validators.required, Validators.maxLength(50)]],
      jobDescription: ['', Validators.required],
      companyName: ['', Validators.required],
      companyEmail: ['', [Validators.required, Validators.email]],
      location: ['', Validators.required],
      employmentTypeId: ['', Validators.required],
      validUntil: ['', Validators.required],
      category: ['', Validators.required],
      bannerImage: [null],
      rowVersion: [''],
    });
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/admin' };
    this.breadcrumbItems = [
      { label: 'Job Managemant' },
      { label: 'Create Job Posting' },
    ];
  }

  initCategoryForm(): void {
    this.jobCategoryForm = this.fb.group({
      jobCategoryName: ['', Validators.required],
    });
  }
  loadEmploymentTypes(): void {
    this.employmentTypeService.getEmploymentTypes().subscribe({
      next: (data) => {
        this.employmentTypes = data;
        if (this.isUpdateMode && this.updateJobPostId) {
          this.refreshEmploymentTypeSelection();
        }
      },
      error: (error) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load employment types',
        });
      },
    });
  }

  private refreshEmploymentTypeSelection(): void {
    const currentValue = this.jobForm.get('employmentTypeId')?.value;
    if (
      currentValue &&
      typeof currentValue === 'object' &&
      currentValue.employmentTypeId
    ) {
      const targetId = currentValue.employmentTypeId;
      const matchingType = this.employmentTypes.find(
        (et) => et.employmentTypeId === targetId
      );
      if (matchingType) {
        this.jobForm.patchValue({ employmentTypeId: matchingType });
      }
    }
  }
  loadJobCategory() {
    this.jobCategoryService.getCategories().subscribe({
      next: (response: HttpResponse<JobCategory[]>) => {
        if (response.body) {
          this.categories = response.body;
          if (this.isUpdateMode && this.updateJobPostId) {
            this.refreshCategorySelection();
          }
        }
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load job categories',
        });
      },
    });
  }

  private refreshCategorySelection(): void {
    const currentValue = this.jobForm.get('category')?.value;
    if (
      currentValue &&
      typeof currentValue === 'object' &&
      currentValue.jobCategoryId
    ) {
      const targetId = currentValue.jobCategoryId;
      const matchingCategory = this.categories.find(
        (c) => c.jobCategoryId === targetId
      );
      if (matchingCategory) {
        this.jobForm.patchValue({ category: matchingCategory });
      }
    }
  }

  loadCompanies() {
    this.companyService.getCompanies().subscribe({
      next: (response: HttpResponse<Company[]>) => {
        if (response.body) {
          this.companies = response.body;
        }
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load companies',
        });
      },
    });
  }

  filterCompanies(event: any) {
    let query = event.query;
    this.filteredCompanies = this.companies.filter((company) =>
      company.companyName.toLowerCase().includes(query.toLowerCase())
    );
  }

  onCompanySelect(event: any) {
    this.selectedCompany = event;
    this.jobForm.patchValue({
      location: this.selectedCompany.value.address,
      companyEmail: this.selectedCompany.value.emailAddress,
    });
  }

  onBannerUpload(event: any): void {
    if (event.files && event.files.length > 0) {
      const file = event.files[0];

      if (this.isUpdateMode && file.size > 0) {
        this.isJobBannerUpdate = true;
      }

      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        this.messageService.add({
          severity: 'error',
          summary: 'Invalid File',
          detail: 'Please upload only image files (JPEG, PNG)',
        });
        return;
      }

      if (file.size > 5242880) {
        this.messageService.add({
          severity: 'error',
          summary: 'File Too Large',
          detail: 'Image size should be less than 5MB',
        });
        return;
      }
      this.jobPostBanner = file;

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          this.previewImage = this.sanitizer.bypassSecurityTrustUrl(
            e.target.result as string
          );
        }
      };
      reader.readAsDataURL(file);

      this.messageService.add({
        severity: 'info',
        summary: 'File Selected',
        detail: 'Job Banner successfully selected',
      });
    }
  }

  private convertBannerToBase64(
    file: File,
    callback: (bannerBase64: string, fileName: string) => void
  ): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      // Get base64 string by removing data:image/xxx;base64, prefix
      const base64String = reader.result as string;
      const base64Content = base64String.split(',')[1];
      callback(base64Content, file.name);
    };
  }

  loadUpdatePostDetails(): void {
    this.route.queryParams.subscribe(this.handleQueryParams.bind(this));
  }

  private handleQueryParams(params: any): void {
    const jobId = params['jobId'];

    if (!jobId) return;

    this.isUpdateMode = true;
    this.updateJobPostId = jobId;

    this.loadJobDetails(jobId);
  }

  private loadJobDetails(jobId: string): void {
    this.jobBankService.getJobBankById(jobId).subscribe({
      next: (jobDetails) => {
        this.updateJobPostRowVersion = jobDetails.rowVersion;

        // Format date properly
        let validUntil;
        if (jobDetails.validUntil) {
          validUntil = new Date(jobDetails.validUntil);
        } else {
          validUntil = new Date();
        }

        // Find the company in the companies list
        const matchingCompany = this.companies.find(
          (c) => c.companyCode === jobDetails.companyCode
        );

        const employmentTypeValue = {
          employmentTypeId: jobDetails.employmentTypeId,
          employmentTypeName: jobDetails.employmentTypeName,
        };
        const categoryValue = {
          jobCategoryId: jobDetails.jobCategoryId,
          jobCategoryName: jobDetails.jobCategory,
        };

        // Find the real objects if already loaded
        const employmentType =
          this.employmentTypes.find(
            (et) => et.employmentTypeId === jobDetails.employmentTypeId
          ) || employmentTypeValue;

        const category =
          this.categories.find(
            (c) => c.jobCategoryId === jobDetails.jobCategoryId
          ) || categoryValue;

        this.jobForm.patchValue({
          jobTitle: jobDetails.jobTitle,
          jobDescription: jobDetails.jobDescription,
          companyName: matchingCompany ?? {
            companyName: jobDetails.companyName,
            companyCode: jobDetails.companyCode,
          },
          companyEmail: jobDetails.companyEmail,
          location: jobDetails.location,
          jobSummary: jobDetails.jobSummary,
          category: category,
          employmentTypeId: employmentType,
          validUntil: validUntil,
        });

        if (jobDetails.bannerUrl) {
          this.previewImage = jobDetails.bannerUrl;
        }
        this.selectedBannerUrl = jobDetails.bannerUrl;

        this.refreshEmploymentTypeSelection();
        this.refreshCategorySelection();
      },
      error: this.handleJobDetailsError.bind(this),
    });
  }

  private handleJobDetailsError(error: any): void {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load job details',
    });
    console.error('Error loading job details:', error);
  }

  addJobPost(): void {
    if (this.jobForm.valid) {
      if (this.jobPostBanner) {
        this.convertBannerToBase64(
          this.jobPostBanner,
          (bannerBase64, fileName) => {
            const jobData = this.createJobDataObject(bannerBase64, fileName);
            this.sendJobData(jobData);
          }
        );
      } else {
        const jobData = this.createJobDataObject('', '');
        this.sendJobData(jobData);
      }
    }
  }

  updateJobPost(): void {
    if (this.jobForm.valid) {
      if (this.jobPostBanner) {
        this.convertBannerToBase64(
          this.jobPostBanner,
          (bannerBase64, fileName) => {
            const jobData = this.createJobDataObject(bannerBase64, fileName);
            // Add job id and rowVersion for update
            const updateData = {
              ...jobData,
              jobId: this.updateJobPostId,
              rowVersion: this.updateJobPostRowVersion,
              isBannerChanged: this.isJobBannerUpdate,
            };
            this.sendUpdateJobData(updateData);
          }
        );
      } else {
        const jobData = this.createJobDataObject('Test', 'data.jpeg');
        // Add job id and rowVersion for update
        const updateData = {
          ...jobData,
          jobId: this.updateJobPostId,
          rowVersion: this.updateJobPostRowVersion,
          isBannerChanged: this.isJobBannerUpdate,
        };
        this.sendUpdateJobData(updateData);
      }
    }
  }

  private createJobDataObject(bannerBase64: string, fileName: string): any {
    const formValue = this.jobForm.value;
    return {
      jobTitle: this.jobForm.value.jobTitle,
      jobSummary: this.jobForm.value.jobSummary,
      jobDescription: this.jobForm.value.jobDescription,
      companyCode: formValue.companyName?.companyCode,
      validUntil: new Date(this.jobForm.value.validUntil).toISOString(),
      employmentTypeId: this.jobForm.value.employmentTypeId.employmentTypeId,
      jobCategoryId: this.jobForm.value.category.jobCategoryId,
      companyEmail: this.jobForm.value.companyEmail,
      location: this.jobForm.value.location,
      bannerBase64: bannerBase64,
      fileName: fileName,
      isBannerChanged: this.isJobBannerUpdate,
      bannerUrl: this.selectedBannerUrl,
      companyName: formValue.companyName?.companyName,
      employmentTypeName:
        this.jobForm.value.employmentTypeId.employmentTypeName,
    };
  }

  private sendJobData(jobData: JobBank): void {
    this.jobBankService.addJobBank(jobData).subscribe({
      next: (response) => {
        if (response.status === 200) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Job posted successfully',
          });
          this.jobForm.reset();
          this.previewImage = null;
          this.jobPostBanner = null;
          setTimeout(() => {
            this.router.navigate(['/admin/job-posting/job-post-management']);
          }, 500);
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to post job',
          });
        }
      },
      error: (error) => {
        console.error('There was an error!', error.error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to post job. Please check console.',
        });
      },
    });
  }

  private sendUpdateJobData(jobData: JobBankUpdate): void {
    this.jobBankService.updateJobBank(jobData).subscribe({
      next: (response) => {
        if (response.status === 200) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Job updated successfully',
          });
          this.jobForm.reset();
          this.previewImage = null;
          this.jobPostBanner = null;
          this.isUpdateMode = false;
          setTimeout(() => {
            this.router.navigate(['/admin/job-posting/job-post-management']);
          }, 500);
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update job',
          });
        }
      },
      error: (error) => {
        console.error('There was an error!', error.error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update job. Please check console.',
        });
      },
    });
  }

  showAddCategoryDialog(): void {
    this.jobCategoryForm.reset();
    this.displayCategoryDialog = true;
  }

  addNewJobCategory(): void {
    if (this.jobCategoryForm.valid) {
      const jobCategoryData = {
        ...this.jobCategoryForm.value,
      };

      this.jobCategoryService
        .addCategory(jobCategoryData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Job category added successfully',
            });
            this.jobCategoryForm.reset();
            this.displayCategoryDialog = false;
            this.loadJobCategory();
          },
          error: (error) => {
            console.error('Error creating job category:', error);
            if (error.status === 409 || error?.error?.includes('duplicate')) {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'This job category already exists',
              });
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to create job category',
              });
            }
          },
        });
    }
  }

  cancelUpdate(): void {
    this.router.navigate(['/admin/job-posting/job-post-management']);
    this.isUpdateMode = false;
    this.jobForm.reset();
  }
}
