import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { ToolbarModule } from 'primeng/toolbar';
import { JobBankService } from '../../../../../services/shared/job-bank.service';
import { ToastModule } from 'primeng/toast';
import { MessageService, ConfirmationService, MenuItem } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { PaginatorModule } from 'primeng/paginator';
import { Posts } from '../../../../../models/shared/job-bank/job-post';
import { JobBankView } from '../../../../../models/shared/job-bank/job-bank';
import { HttpResponse } from '@angular/common/http';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';


@Component({
  selector: 'app-job-post-management',
  imports: [
    TableModule,
    CommonModule,
    ButtonModule,
    ToastModule,
    ConfirmDialogModule,
    PaginatorModule,
    TagModule,
    IconFieldModule,
    InputIconModule,
    ToolbarModule,
    ReactiveFormsModule,
    InputTextModule,
    BreadcrumbModule,
  ],
  templateUrl: './job-post-management.component.html',
  styleUrl: './job-post-management.component.css',
  providers: [MessageService, ConfirmationService],
})
export class JobPostManagementComponent implements OnInit {
  private readonly jobBankService = inject(JobBankService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();
  private readonly searchTerms = new Subject<string>();

  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
  posts!: JobBankView[];
  deleteDialogVisible: boolean = false;
  isDeletionConfirmed: boolean = false;
  selectedPost?: Posts;
  searchTimeout: any;

  searchControl: FormControl = new FormControl("");
  pageNumber: number = 1;
  pageSize: number = 10;
  totalRecords: number = 0;

  onPageChange(event: any) {
    this.pageNumber = Math.floor(event.first / event.rows) + 1;
    this.pageSize = event.rows;
    this.getPosts(this.pageNumber, this.pageSize);
  }

  ngOnInit(): void {
    this.getPosts(this.pageNumber, this.pageSize);
    this.initializeBreadcrumb();
    this.setupSearch();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: "pi pi-home", routerLink: "/admin" };
    this.breadcrumbItems = [{ label: "Job post managemant" }];
  }

  private setupSearch(): void {
    this.searchTerms.pipe(
      takeUntil(this.destroy$),
      debounceTime(1000),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.pageNumber = 1;
      this.searchJobs(searchTerm);
    });
  }

  onSearchChange(event: Event): void {
    const searchTerm = this.searchControl.value;
    this.searchTerms.next(searchTerm);
  }

  private searchJobs(searchTerm: string): void {
    this.jobBankService
      .searchJobs(this.pageNumber, this.pageSize, searchTerm)
      .subscribe({
        next: (response: HttpResponse<JobBankView[]>) => {
          if (response.body) {
            this.posts = response.body;
          }
          const paginationHeader: string | null =
            response.headers.get("Pagination");
          if (paginationHeader) {
            const pagination = JSON.parse(paginationHeader);
            this.totalRecords = pagination.totalItems;
          }
        },
        error: () => {
          this.messageService.add({
            severity: "error",
            summary: "Error",
            detail: "Failed to load job posts",
          });
        },
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getPosts(pageNumber: number, pageSize: number): void {
    this.jobBankService
      .getJobBanks(pageNumber, pageSize)
      .subscribe({
        next: (response: HttpResponse<JobBankView[]>) => {
          if (response.body) {
            this.posts = response.body;
          }
          const paginationHeader: string | null =
            response.headers.get("Pagination");
          if (paginationHeader) {
            const pagination = JSON.parse(paginationHeader);
            this.totalRecords = pagination.totalItems;
          }
        },
        error: () => {
          this.messageService.add({
            severity: "error",
            summary: "Error",
            detail: "Failed to load job posts",
          });
        },
      });
  }

  deletePost(id: string) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this job post?',
      header: 'Delete Confirmation',
      icon: 'pi pi-exclamation-triangle',

      accept: () => {
        this.jobBankService.deleteJobBank(id).subscribe({
          next: (response) => {
            if (response.status === 200) {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Job posted deleted successfully',
              });
              this.deleteDialogVisible = false;
              this.getPosts(this.pageNumber, this.pageSize);
            }
          },
          error: (_) => {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete the job posts',
            });
            this.deleteDialogVisible = false;
          },
        });
      },
      reject: () => {
        this.messageService.add({
          severity: 'info',
          summary: 'Cancelled',
          detail: 'Delete cancelled',
        });
      },
    });
  }

  updatePost(post: Posts) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to update this job post?',
      header: 'Update Confirmation',
      icon: 'pi pi-info-circle',
      accept: () => {
        this.router.navigate(['/admin/job-posting/create-job-posting'], {
          queryParams: {
            jobId: post.jobId,
          },
        });
      },
      reject: () => {
        this.messageService.add({
          severity: 'info',
          summary: 'Cancelled',
          detail: 'Update cancelled',
        });
      },
    });
  }

  navigateToAddJob() {
    this.router.navigate(['/admin/job-posting/create-job-posting']);
  }
}
