<p-toast></p-toast>
<p-confirmdialog acceptButtonStyleClass="p-button-warn" rejectButtonStyleClass="p-button-secondary" />

<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-3xl">Job Post Managemant</span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"
                styleClass="custom-breadcrumb"></p-breadcrumb>
        </div>
    </div>
  </div>

<p-toolbar styleClass="mb-6 mx-4 py-4 md:p-3">
    <ng-template #start>
      <p-button label="Add New Job Post" icon="pi pi-plus" severity="primary" class="mr-2"
        (onClick)="navigateToAddJob()"></p-button>
    </ng-template>
    <ng-template #end>
        <div>
            <p-iconfield>
                <p-inputicon styleClass="pi pi-search"></p-inputicon>
                <input pInputText type="text" [formControl]="searchControl" placeholder="Search"
                    (input)="onSearchChange($event)" />
            </p-iconfield>
        </div>
    </ng-template>
  </p-toolbar>

<div class="shadow-md mx-4 py-4 md:p-3 rounded-lg bg-white">
    <div class="card">
        <p-table [value]="posts" stripedRows [tableStyle]="{ 'min-width': '50rem' }" *ngIf="posts && posts.length > 0">
            <ng-template pTemplate="header">
                <tr>
                    <th scope="col">No</th>
                    <th scope="col" style="width:20%">Title</th>
                    <th scope="col">Catagory</th>
                    <th scope="col" style="width:15%">Company</th>
                    <th scope="col" style="text-align:center">Employment Type</th>
                    <th scope="col">Valid Date</th>
                    <th scope="col">Actions</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-post let-i="rowIndex">
                <tr>
                    <td>{{ i + 1 }}</td>
                    <td style="width:20%">{{ post.jobTitle }}</td>
                    <td>{{ post.jobCategory }}</td>
                    <td style="width:15%">{{ post.companyName }}</td>
                    <td style="text-align:center">
                        <p-tag [severity]="post.employmentTypeName === 'Part-Time' ? 'info' : 
                                         post.employmentTypeName === 'Full-Time' ? 'success' : 
                                         post.employmentTypeName === 'Hybrid' ? 'warn' : 'success'"
                            [value]="post.employmentTypeName"></p-tag>
                    </td>
                    <td>{{ post.validUntil | date:'yyyy-MM-dd' }}</td>
                    <td>
                        <div class="flex gap-2">
                            <button pButton type="button" icon="pi pi-pencil"
                                class="p-button-rounded p-button-success p-button-text" (click)="updatePost(post)">
                            </button>
                            <button pButton type="button" icon="pi pi-trash"
                                class="p-button-rounded p-button-danger p-button-text" (click)="deletePost(post.jobId)">
                            </button>
                        </div>
                    </td>
                </tr>
            </ng-template>
        </p-table>

        <div *ngIf="!posts || posts.length === 0" class="flex items-center justify-center h-64">
            <p class="text-lg text-gray-500 font-medium">
                No jobs available at the moment.
            </p>
        </div>

        <p-paginator [rows]="pageSize" [totalRecords]="totalRecords" [showCurrentPageReport]="true"
            [rowsPerPageOptions]="[10, 15, 20, 50]"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
            (onPageChange)="onPageChange($event)" *ngIf="posts && posts.length > 0"></p-paginator>
    </div>
</div>