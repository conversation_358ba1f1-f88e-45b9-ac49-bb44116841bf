<p-toast></p-toast>
<p-confirmdialog acceptButtonStyleClass="p-button-warn" rejectButtonStyleClass="p-button-secondary"></p-confirmdialog>

<div class="w-full px-4 py-4">
  <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
    <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
      <span class="text-white font-semibold text-3xl">Job Applications</span>
      <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"
        styleClass="custom-breadcrumb"></p-breadcrumb>
    </div>
  </div>
</div>

<p-toolbar styleClass="mb-6 mx-4 py-4 md:p-3">
  <ng-template pTemplate="start">
  </ng-template> <ng-template pTemplate="end">
    <p-iconfield>
      <p-inputicon styleClass="pi pi-search"></p-inputicon>
      <input pInputText type="text" placeholder="Search..." [formControl]="searchControl"
        (input)="onSearchChange($event)" />
    </p-iconfield>
  </ng-template>
</p-toolbar>

<div class="shadow-md mx-4 py-4 md:p-3 rounded-lg bg-white">
  <div class="card">
    <p-table [value]="filteredApplications" [tableStyle]="{ 'min-width': '50rem' }"
      *ngIf="filteredApplications && filteredApplications.length > 0">
      <ng-template pTemplate="header">
        <tr>
          <th scope="col">No</th>
          <th scope="col">Job Title</th>
          <th scope="col">Company Name</th>
          <th scope="col">Name</th>
          <th scope="col">Student Id</th>
          <th scope="col">Email</th>
          <th scope="col">NIC</th>
        </tr>
      </ng-template> <ng-template pTemplate="body" let-application let-i="rowIndex">
        <tr>
          <td>{{ (pageNumber - 1) * pageSize + i + 1 }}</td>
          <td>{{ application.jobTitle }}</td>
          <td>{{ application.companyName }}</td>
          <td>{{ application.name }}</td>
          <td>{{ application.studentId }}</td>
          <td>{{ application.email }}</td>
          <td>{{ application.nic }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="7" class="text-center p-4">
            <div *ngIf="jobApplications.length === 0" class="flex flex-col items-center">
              <i class="pi pi-info-circle" style="font-size: 2rem"></i>
              <p>No job applications found.</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>

    <p-paginator [rows]="pageSize" [totalRecords]="totalRecords" [showCurrentPageReport]="true"
      [rowsPerPageOptions]="[10, 15, 20, 50]"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
      (onPageChange)="onPageChange($event)"></p-paginator>
  </div>
</div>