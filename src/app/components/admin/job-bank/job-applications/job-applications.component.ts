import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { ToolbarModule } from 'primeng/toolbar';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { PaginatorModule } from 'primeng/paginator';
import { InputTextModule } from 'primeng/inputtext';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { JobApplicationService } from '../../../../services/shared/job-application.service';
import { MessageService, ConfirmationService, MenuItem } from 'primeng/api';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { JobApplication } from '../../../../models/admin/job-bank/job-application';
import { HttpResponse } from '@angular/common/http';

@Component({
  selector: 'app-job-applications',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    ButtonModule,
    TagModule,
    ToolbarModule,
    ToastModule,
    ConfirmDialogModule,
    DialogModule,
    PaginatorModule,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    ReactiveFormsModule,
    BreadcrumbModule,
  ],
  templateUrl: './job-applications.component.html',
  styleUrl: './job-applications.component.css',
  providers: [MessageService, ConfirmationService],
})
export class JobApplicationsComponent implements OnInit, OnDestroy {
  private readonly jobApplicationService = inject(JobApplicationService);
  private readonly messageService = inject(MessageService);
  private readonly destroy$ = new Subject<void>();
  private readonly searchTerms = new Subject<string>();

  jobApplications: JobApplication[] = [];
  filteredApplications: JobApplication[] = [];
  selectedApplication: JobApplication | null = null;
  detailsVisible = false;
  searchControl = new FormControl('');

  pageNumber: number = 1;
  pageSize: number = 10;
  totalRecords: number = 0;
  first: number = 0;

  // Breadcrumb items
  breadcrumbItems: MenuItem[] = [{ label: 'Job Applications' }];
  home: MenuItem = { icon: 'pi pi-home', routerLink: '/admin' };

  ngOnInit() {
    this.loadApplications(this.pageNumber, this.pageSize);
    this.setupSearch();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSearch(): void {
    this.searchTerms
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(1000),
        distinctUntilChanged()
      )
      .subscribe((searchTerm) => {
        this.pageNumber = 1;
        this.searchJobApplications(searchTerm);
      });
  }
  onSearchChange(event: Event): void {
    const searchTerm = this.searchControl.value ?? '';
    this.searchTerms.next(searchTerm);
  }

  private searchJobApplications(searchTerm: string): void {
    this.jobApplicationService
      .searchApplications(this.pageNumber, this.pageSize, searchTerm)
      .subscribe({
        next: (response: HttpResponse<JobApplication[]>) => {
          if (response.body) {
            this.jobApplications = response.body;
            this.filteredApplications = response.body;
          }
          const paginationHeader: string | null =
            response.headers.get('Pagination');
          if (paginationHeader) {
            const pagination = JSON.parse(paginationHeader);
            this.totalRecords = pagination.totalItems;
          }
        },
        error: () => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to search job applications',
          });
        },
      });
  }

  loadApplications(pageNumber: number, pageSize: number) {
    this.jobApplicationService
      .getAllJobApplications(pageNumber, pageSize)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.jobApplications = response.body ?? [];
          this.filteredApplications = response.body ?? [];
          const paginationHeader = response.headers.get('Pagination');
          if (paginationHeader) {
            const pagination = JSON.parse(paginationHeader);
            this.totalRecords = pagination.totalItems;
          } else {
            this.totalRecords = parseInt(
              response.headers.get('X-Pagination-Total-Count') ?? '0'
            );
          }
        },
        error: (error) => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load job applications',
          });
          console.error('Error loading job applications:', error);
        },
      });
  }

  onPageChange(event: any): void {
    this.pageNumber = Math.floor(event.first / event.rows) + 1;
    this.pageSize = event.rows;
    this.first = event.first;

    if (this.searchControl.value && this.searchControl.value.trim() !== '') {
      this.searchJobApplications(this.searchControl.value);
    } else {
      this.loadApplications(this.pageNumber, this.pageSize);
    }
  }

  showDetails(application: JobApplication) {
    this.selectedApplication = application;
    this.detailsVisible = true;
  }

  hideDetails() {
    this.detailsVisible = false;
    this.selectedApplication = null;
  }
}
