/* Custom breadcrumb styling */
:host ::ng-deep .custom-breadcrumb {
  background-color: transparent !important;
}

:host ::ng-deep .custom-breadcrumb .p-breadcrumb-chevron {
  color: white !important;
}

:host ::ng-deep .custom-breadcrumb .p-menuitem-text {
  color: white !important;
}

:host ::ng-deep .custom-breadcrumb .p-breadcrumb-home .p-menuitem-icon {
  color: white !important;
}

/* Status tag styling adjustments */
:host ::ng-deep .p-tag {
  min-width: 80px;
  justify-content: center;
}
