import { Component, inject, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { JobCategoryService } from '../../../../../services/admin/job-bank/job-category.service';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { JobCategory } from '../../../../../models/shared/job-bank/job-category';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { HttpResponse } from '@angular/common/http';
import { Subject, takeUntil } from 'rxjs';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { ToolbarModule } from 'primeng/toolbar';
import { PaginatorModule } from 'primeng/paginator';
import { InputTextModule } from 'primeng/inputtext';
import { BreadcrumbModule } from 'primeng/breadcrumb';

@Component({
  selector: 'app-job-category-manager',
  templateUrl: './job-category-manager.component.html',
  styleUrls: ['./job-category-manager.component.css'],
  imports: [
    ReactiveFormsModule,
    TableModule,
    DialogModule,
    ButtonModule,
    CommonModule,
    ConfirmDialogModule,
    ToastModule,
    IconFieldModule,
    InputIconModule,
    ToolbarModule,
    PaginatorModule,
    InputTextModule,
    BreadcrumbModule,
  ],
  providers: [MessageService, ConfirmationService],
})
export class JobCategoryManagerComponent implements OnInit {
  private readonly jobCategoryService = inject(JobCategoryService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly fb = inject(FormBuilder);
  private readonly destroy$ = new Subject<void>();

  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
  jobCategories: JobCategory[] = [];
  selectedJobCategory!: JobCategory;
  jobCategoryForm!: FormGroup;
  isUpdateMode: boolean = false;
  showDialogBox: boolean = false;

  totalRecords: number = 0;
  pageSize: number = 10;
  pageNumber: number = 1;

  onPageChange(event: any) {
    this.pageNumber = Math.floor(event.first / event.rows) + 1;
    this.pageSize = event.rows;
    this.getJobCategories(this.pageNumber, this.pageSize);
    this.initializeBreadcrumb();
  }

  ngOnInit(): void {
    this.initialize();
    this.initForm();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: "pi pi-home", routerLink: "/admin" };
    this.breadcrumbItems = [
      { label: 'Job Posting' },
      { label: 'Job category' },
    ];
  }

  private initForm(): void {
    this.jobCategoryForm = this.fb.group({
      jobCategoryName: ['', Validators.required],
    });
  }

  private initialize(): void {
    this.getJobCategories(this.pageNumber, this.pageSize);
  }

  private getJobCategories(pageNumber: number, pageSize: number) {
    this.jobCategoryService.getCategoriesPaged(pageNumber, pageSize).subscribe({
      next: (response: HttpResponse<JobCategory[]>) => {
        if (response.body) {
          this.jobCategories = response.body;
        }
        const paginationHeader: string | null =
          response.headers.get('Pagination');
        if (paginationHeader) {
          const pagination = JSON.parse(paginationHeader);
          this.totalRecords = pagination.totalItems;
        }
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to job categories',
        });
      },
    });
  }

  deleteJobCategory(jobCategoryId: string) {
    if (!jobCategoryId) {
      return;
    }

    this.confirmationService.confirm({
      message: 'Are you sure you want to delete this job category?',
      header: 'Delete Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.jobCategoryService
          .deleteCategory(jobCategoryId)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Job category deleted successfully',
              });
              this.getJobCategories(this.pageNumber, this.pageSize);
            },
            error: (error) => {
              console.error('Error deleting job category:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete job category',
              });
            },
          });
      },
    });
  }

  addJobCategoryDialogBox(): void {
    this.isUpdateMode = false;
    this.jobCategoryForm.reset();
    this.showDialogBox = true;
  }

  addNewJobCategory() {
    if (this.jobCategoryForm.valid) {
      const jobCategoryData = {
        ...this.jobCategoryForm.value,
      };

      this.jobCategoryService
        .addCategory(jobCategoryData)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Job category added successfully',
            });
            this.jobCategoryForm.reset();
            this.showDialogBox = false;
            this.getJobCategories(this.pageNumber, this.pageSize);
          },
          error: (error) => {
            console.error('Error creating job category:', error);
            if (error.status === 409 || error?.error?.includes('duplicate')) {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'This job category already exists',
              });
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to create job category',
              });
            }
          },
        });
    }
  }

  updateJobCategoryDialogBox(jobCategory: JobCategory) {
    this.isUpdateMode = true;
    this.selectedJobCategory = jobCategory
    this.jobCategoryForm.patchValue({
      jobCategoryName: this.selectedJobCategory.jobCategoryName
    });
    this.showDialogBox = true;
  }

  updateJobcategory() {
    if (this.jobCategoryForm.valid && this.selectedJobCategory) {
      const updatedData = {
        jobCategoryId: this.selectedJobCategory.jobCategoryId,
        jobCategoryName: this.jobCategoryForm.value.jobCategoryName
      };

      this.confirmationService.confirm({
        message: 'Are you sure you want to update this job category?',
        header: 'Update Confirmation',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          this.jobCategoryService
            .updateCategory(updatedData)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: () => {
                this.messageService.add({
                  severity: 'success',
                  summary: 'Success',
                  detail: 'Job category updated successfully'
                });
                this.jobCategoryForm.reset();
                this.showDialogBox = false;
                this.getJobCategories(this.pageNumber, this.pageSize);
              },
              error: (error) => {
                console.error('Error updating job category:', error);
                this.messageService.add({
                  severity: 'error',
                  summary: 'Error',
                  detail: 'Failed to update job category'
                });
              }
            });
        }
      });
    }
  }
}
