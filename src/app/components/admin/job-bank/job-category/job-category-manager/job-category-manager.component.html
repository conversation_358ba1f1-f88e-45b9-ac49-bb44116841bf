<div class="w-full px-4 py-4">
  <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
      <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
          <span class="text-white font-semibold text-3xl">Job Category Managemant</span>
          <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
      </div>
  </div>
</div>

<p-toolbar styleClass="mb-6 mx-4 py-4 md:p-3">
  <ng-template #start>
    <p-button label="Add New Category" icon="pi pi-plus" severity="primary" class="mr-2"
      (onClick)="addJobCategoryDialogBox()"></p-button>
  </ng-template>
  <ng-template #end>
    <p-iconfield>
      <p-inputicon styleClass="pi pi-search"></p-inputicon>
      <input pInputText type="text" placeholder="Search..." />
    </p-iconfield>
  </ng-template>
</p-toolbar>

<div class="shadow-md mx-4 py-4 md:p-3 rounded-lg bg-white">
  <p-table [value]="jobCategories" stripedRows dataKey="id" [tableStyle]="{'width': '100%'}" class="mx-auto">
    <ng-template pTemplate="header">
      <tr>
        <th scope="col" style="width:30%;">ID</th>
        <th scope="col" style="width:30%;">Name</th>
        <th scope="col" style="width:30%; text-align:center">Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-category let-i="rowIndex">
      <tr>
        <td style="width:30%;">{{ i + 1 }}</td>
        <td style="width:30%;">{{ category.jobCategoryName }}</td>
        <td style="width:30%; text-align:center">
          <div class="flex justify-center gap-2">
            <button pButton pRipple type="button" icon="pi pi-pencil" rounded outlined severity="info"
              (click)="updateJobCategoryDialogBox(category)"></button>
            <button pButton pRipple type="button" icon="pi pi-trash" rounded outlined severity="danger"
              (click)="deleteJobCategory(category.jobCategoryId)"></button>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>

  <p-paginator [rows]="pageSize" [totalRecords]="totalRecords" [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[10, 15, 20, 50]"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
    (onPageChange)="onPageChange($event)"></p-paginator>
</div>

<div class="card flex justify-content-center">
  <p-toast></p-toast>
  <p-confirmDialog acceptButtonStyleClass="p-button-warn" rejectButtonStyleClass="p-button-secondary"></p-confirmDialog>
</div>

<p-dialog header="{{ isUpdateMode ? 'Update Job Category' : 'Add Job Category' }}" [(visible)]="showDialogBox"
  [modal]="true" [style]="{ width: '30rem' }" [closable]="true">
  <form [formGroup]="jobCategoryForm" (ngSubmit)="isUpdateMode ? updateJobcategory() : addNewJobCategory()">
    <div class="p-fluid formgrid grid">
      <div class="field col-12">
        <label for="jobCategoryName" class="font-semibold mb-1">Job Category Name</label>
        <input pInputText id="jobCategoryName" formControlName="jobCategoryName" class="p-inputtext-sm mt-2 mb-3"
          [style]="{width: '100%', border: '1px solid #ced4da'}" placeholder="Enter the job category" />
        <small *ngIf="(jobCategoryForm.get('jobCategoryName')?.touched)" class="p-error block mt-1 text-red-500">
          Category name is required.
        </small>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="col-12 flex justify-content-end gap-3 mt-4">
      <p-button label="Cancel" severity="secondary" icon="pi pi-times" class="p-button-text"
        (onClick)="showDialogBox = false"></p-button>
      <p-button *ngIf="!isUpdateMode" label="Save" type="submit" icon="pi pi-check" severity="success"
        [disabled]="jobCategoryForm.invalid"></p-button>
      <p-button *ngIf="isUpdateMode" label="Update" type="submit" icon="pi pi-sync" severity="info"
        [disabled]="jobCategoryForm.invalid"></p-button>
    </div>
  </form>
</p-dialog>