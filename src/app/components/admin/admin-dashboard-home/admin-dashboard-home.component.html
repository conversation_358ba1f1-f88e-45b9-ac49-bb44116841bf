<div class="bg-gray-100 min-h-screen px-4 pt-4 md:p-6">
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
    <!-- User Management -->
    <div class="bg-white shadow-lg rounded-2xl p-4 md:p-6">
      <h3 class="text-lg font-semibold mb-4">User Management</h3>
      <p class="text-gray-600 text-sm mb-2">Total Users: {{ userManagement.totalUsers }}</p>
      <p class="text-gray-600 text-sm">Pending Approvals: {{ userManagement.pendingApprovals }}</p>
    </div>

    <!-- Course Management -->
    <div class="bg-white shadow-lg rounded-2xl p-4 md:p-6">
      <h3 class="text-lg font-semibold mb-4">Course Management</h3>
      <p class="text-gray-600 text-sm mb-2">Total Courses: {{ courseManagement.totalCourses }}</p>
      <p class="text-gray-600 text-sm">Active Courses: {{ courseManagement.activeCourses }}</p>
    </div>

    <!-- Job Postings -->
    <div class="bg-white shadow-lg rounded-2xl p-4 md:p-6">
      <h3 class="text-lg font-semibold mb-4">Job Postings</h3>
      <p class="text-gray-600 text-sm mb-2">Total Jobs: {{ jobPostings.totalJobs }}</p>
      <p class="text-gray-600 text-sm">Pending Jobs: {{ jobPostings.pendingJobs }}</p>
    </div>

    <!-- Student Reports -->
    <div class="bg-white shadow-lg rounded-2xl p-4 md:p-6">
      <h3 class="text-lg font-semibold mb-4">Student Reports</h3>
      <p class="text-gray-600 text-sm mb-2">Total Reports: {{ studentReports.totalReports }}</p>
      <p class="text-gray-600 text-sm">Pending Reviews: {{ studentReports.pendingReviews }}</p>
    </div>

    <!-- Enrollment Trends Chart -->
    <div class="bg-white shadow-lg rounded-2xl p-4 md:p-6">
      <h3 class="text-lg font-semibold mb-4">Enrollment Trends</h3>
      <canvas #demoChart style="width: 100%; height: 200px;"></canvas>
    </div>

    <!-- System Overview Card -->
    <div class="bg-white shadow-lg rounded-2xl p-4 md:p-6">
      <h3 class="text-lg font-semibold mb-4">System Overview</h3>
      <ul class="space-y-2">
        <li class="text-gray-600 text-sm">Active Sessions: 42</li>
        <li class="text-gray-600 text-sm">Server Load: 68%</li>
        <li class="text-gray-600 text-sm">Pending Tasks: 7</li>
      </ul>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center h-12 w-12 rounded-md bg-orange-100">
            <i class="pi pi-clock text-xl text-orange-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Pending Review</p>
          <p class="text-2xl font-semibold text-gray-900">{{ pendingOrganizations.length }}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100">
            <i class="pi pi-building text-xl text-blue-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">Organizations</p>
          <p class="text-2xl font-semibold text-gray-900">{{ pendingOrganizations.length }}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center h-12 w-12 rounded-md bg-green-100">
            <i class="pi pi-calendar text-xl text-green-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">This Week</p>
          <p class="text-2xl font-semibold text-gray-900">{{ pendingOrganizations.length }}</p>
        </div>
      </div>
    </div>
  </div>
</div>