import { Component, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { Chart, registerables } from 'chart.js';
Chart.register(...registerables);

@Component({
  selector: 'app-admin-dashboard-home',
  templateUrl: './admin-dashboard-home.component.html',
  styleUrls: ['./admin-dashboard-home.component.css']
})
export class AdminDashboardHomeComponent implements AfterViewInit {
  adminName: string = 'Admin User';

  // Demo sections for the admin dashboard
  userManagement = { totalUsers: 120, pendingApprovals: 5 };
  courseManagement = { totalCourses: 15, activeCourses: 12 };
  jobPostings = { totalJobs: 8, pendingJobs: 2 };
  studentReports = { totalReports: 30, pendingReviews: 4 };

  @ViewChild('demoChart') demoChartRef!: ElementRef;
  demoChart: any;
  pendingOrganizations: any[] = [];

  ngAfterViewInit(): void {
    this.createDemoChart();
  }

  createDemoChart() {
    this.demoChart = new Chart(this.demoChartRef.nativeElement, {
      type: 'line',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Enrollment Trends',
          data: [30, 45, 28, 50, 42, 60],
          backgroundColor: 'rgba(75,192,192,0.4)',
          borderColor: 'rgba(75,192,192,1)',
          borderWidth: 2,
          fill: true,
          tension: 0.3
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: { position: 'top' },
          title: { display: true, text: 'Enrollment Trends' }
        }
      }
    });
  }
}
