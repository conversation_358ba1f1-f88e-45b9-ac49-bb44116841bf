import { Routes } from '@angular/router';
import { AdminDashboardHomeComponent } from './admin-dashboard-home/admin-dashboard-home.component';
import { CreateJobPostingComponent } from './job-bank/job-post/create-job-posting/create-job-posting.component';
import { JobCategoryManagerComponent } from './job-bank/job-category/job-category-manager/job-category-manager.component';
import { JobPostManagementComponent } from './job-bank/job-post/job-post-management/job-post-management.component';
import { CompanyRegistrationComponent } from './job-bank/partners/company-registration/company-registration.component';
import { ViewCompaniesComponent } from './job-bank/partners/view-companies/view-companies.component';
import { JobApplicationsComponent } from './job-bank/job-applications/job-applications.component';
import { trainingRoutes } from './training/training.routes';
import { UserRole } from '../../models/auth/auth';

export const adminRoutes: Routes = [
	{
		path: '',
		component: AdminDashboardHomeComponent
	},
    {
		path: 'job-posting',
		children: [
            {
                path: 'create-job-posting',
                component: CreateJobPostingComponent
            },
            {
                path: 'job-category-manager',
                component: JobCategoryManagerComponent
            },
            {
                path: 'job-post-management',
                component: JobPostManagementComponent
            }
        ]
	},
    {
		path: 'training',
		children: trainingRoutes
	},
    {
        path: 'company-management',
        children: [
            {
                path: 'company-registration',
                component: CompanyRegistrationComponent
            },
            {
                path: 'view-companies',
                component: ViewCompaniesComponent
            }
        ]
    },
    {
        path: 'job-applications',
        component: JobApplicationsComponent
    }
];
