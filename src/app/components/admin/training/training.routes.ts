import { Routes } from '@angular/router';
import { TrainingOrganizationApprovalListComponent } from './training-organization/training-organization-approval-list/training-organization-approval-list.component';
import { TrainingOrganizationDetailsComponent } from './training-organization/training-organization-details/training-organization-details.component';
import { CreateQualificationTypeComponent } from './qualification/create-qualification-type/create-qualification-type.component';
import { QualificationTypesComponent } from './qualification/qualification-types/qualification-types.component';
import { CreateProfessionalBodyComponent } from './professional-body/create-professional-body/create-professional-body.component';
import { ProfessionalBodiesComponent } from './professional-body/professional-bodies/professional-bodies.component';
import { CreateQualificationComponent } from './qualification/create-qualification/create-qualification.component';
import { QualificationsComponent } from './qualification/qualifications/qualifications.component';
import { Training404NotFoundComponent } from './training-404-not-found/training-404-not-found.component';
import { TrainingOrganizationRegistrationComponent } from '../../student/training/training-organization-registration/training-organization-registration.component';

export const trainingRoutes: Routes = [
  {
    path: 'organization-approvals',
    component: TrainingOrganizationApprovalListComponent,
  },
  {
    path: 'organization-details/:id',
    component: TrainingOrganizationDetailsComponent,
  },
  {
    path: 'training-organization-registration',
    component: TrainingOrganizationRegistrationComponent,
  },
  {
    path: 'create-qualification-type',
    component: CreateQualificationTypeComponent,
  },
  {
    path: 'qualification-type-management',
    component: QualificationTypesComponent,
  },
  {
    path: 'create-professional-body',
    component: CreateProfessionalBodyComponent,
  },
  {
    path: 'professional-body-management',
    component: ProfessionalBodiesComponent,
  },
  {
    path: 'create-qualification',
    component: CreateQualificationComponent,
  },
  {
    path: 'qualifications',
    component: QualificationsComponent,
  },
  {
    path: '**',
    component: Training404NotFoundComponent,
  },
];
