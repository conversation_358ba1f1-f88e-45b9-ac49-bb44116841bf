<p-toast></p-toast>
<p-confirmDialog></p-confirmDialog>

<!-- Header Section -->
<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-2xl sm:text-3xl">
                {{ isUpdateMode ? 'Edit Professional Body' : 'Create Professional Body' }}
            </span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <div class="max-w-3xl mx-auto">
        <form [formGroup]="professionalBodyForm" (ngSubmit)="onSubmit()">
            
            <!-- Basic Information Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-building text-blue-600"></i>
                        Professional Body Information
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">
                        {{ isUpdateMode ? 'Update the professional body details' : 'Provide information for the new professional body' }}
                    </p>
                </div>

                <div class="space-y-6">
                    <!-- Professional Body Name -->
                    <div>
                        <label for="professionalBodyName" class="block text-sm font-medium text-gray-700 mb-2">
                            Professional Body Name <span class="text-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            id="professionalBodyName" 
                            pInputText 
                            formControlName="professionalBodyName" 
                            class="w-full h-11"
                            placeholder="Enter professional body name (e.g., Institute of Chartered Accountants)"
                            [class.ng-invalid]="isFieldInvalid('professionalBodyName')"
                            maxlength="100">
                        <small *ngIf="isFieldInvalid('professionalBodyName')" 
                               class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('professionalBodyName') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            {{ professionalBodyForm.get('professionalBodyName')?.value?.length || 0 }}/100 characters
                        </small>
                    </div>

                    <!-- Professional Body URL -->
                    <div>
                        <label for="professionalBodyUrl" class="block text-sm font-medium text-gray-700 mb-2">
                            Website URL <span class="text-red-500">*</span>
                        </label>
                        <input 
                            type="url" 
                            id="professionalBodyUrl" 
                            pInputText 
                            formControlName="professionalBodyUrl" 
                            class="w-full h-11"
                            placeholder="Enter website URL (e.g., https://www.example.com)"
                            [class.ng-invalid]="isFieldInvalid('professionalBodyUrl')">
                        <small *ngIf="isFieldInvalid('professionalBodyUrl')" 
                               class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('professionalBodyUrl') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            Include https:// or http:// for proper URL format
                        </small>
                    </div>

                    <!-- Country Based On -->
                    <div>
                        <label for="countryBasedOn" class="block text-sm font-medium text-gray-700 mb-2">
                            Country Based On <span class="text-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            id="countryBasedOn" 
                            pInputText 
                            formControlName="countryBasedOn" 
                            class="w-full h-11"
                            placeholder="Enter country (e.g., United Kingdom, Sri Lanka)"
                            [class.ng-invalid]="isFieldInvalid('countryBasedOn')"
                            maxlength="50">
                        <small *ngIf="isFieldInvalid('countryBasedOn')" 
                               class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('countryBasedOn') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            {{ professionalBodyForm.get('countryBasedOn')?.value?.length || 0 }}/50 characters
                        </small>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                    <!-- Cancel Button -->
                    <p-button 
                        type="button" 
                        icon="pi pi-times" 
                        label="Cancel"
                        styleClass="p-button-outlined w-full sm:w-auto" 
                        (onClick)="onCancel()"
                        [disabled]="isSubmitting">
                    </p-button>

                    <!-- Submit Button -->
                    <p-button 
                        type="submit" 
                        icon="pi pi-check"
                        [label]="isUpdateMode ? 'Update Professional Body' : 'Create Professional Body'"
                        [disabled]="professionalBodyForm.invalid || isSubmitting" 
                        [loading]="isSubmitting"
                        styleClass="p-button-primary w-full sm:w-auto" 
                        [style]="{ 'min-width': '200px' }">
                    </p-button>
                </div>

                <!-- Form Status Message -->
                <div *ngIf="professionalBodyForm.invalid && professionalBodyForm.touched"
                     class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center gap-2">
                        <i class="pi pi-exclamation-triangle text-red-600"></i>
                        <p class="text-sm text-red-800">
                            Please fill in all required fields correctly before submitting.
                        </p>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
