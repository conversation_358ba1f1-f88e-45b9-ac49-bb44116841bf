/* Professional Body Create/Edit Component Styles */

/* Custom input validation styles */
.ng-invalid:not(.ng-pristine) {
  border-color: #fca5a5;
}

.ng-invalid:not(.ng-pristine):focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.ng-valid:not(.ng-pristine) {
  border-color: #86efac;
}

.ng-valid:not(.ng-pristine):focus {
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Custom spinner color */
:host ::ng-deep .custom-spinner .p-progress-spinner-circle {
  stroke: #2563eb;
}

/* Breadcrumb styling within gradient header */
:host ::ng-deep .p-breadcrumb {
  background: transparent;
  border: none;
}

:host ::ng-deep .p-breadcrumb .p-breadcrumb-list {
  background: transparent;
}

:host ::ng-deep .p-breadcrumb .p-menuitem-link {
  color: rgba(255, 255, 255, 0.9);
}

:host ::ng-deep .p-breadcrumb .p-menuitem-link:hover {
  color: white;
}

:host ::ng-deep .p-breadcrumb .p-menuitem-separator {
  color: rgba(255, 255, 255, 0.7);
}

/* Button hover effects */
:host ::ng-deep .p-button:hover {
  transform: translateY(-1px);
  transition: all 0.2s;
}

/* Toast positioning */
:host ::ng-deep .p-toast {
  z-index: 1050;
}