import {
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MenuItem, MessageService, ConfirmationService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { ProfessionalBodyService } from '../../../../../services/admin/training/professional-body.service';
import { ProfessionalBody } from '../../../../../models/admin/training/professional-body.model';

@Component({
  selector: 'app-create-professional-body',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './create-professional-body.component.html',
  styleUrl: './create-professional-body.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateProfessionalBodyComponent implements OnInit, OnDestroy {
  private readonly fb = inject(FormBuilder);
  private readonly professionalBodyService = inject(ProfessionalBodyService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly destroy$ = new Subject<void>();

  // Component state
  isSubmitting = false;
  isUpdateMode = false;
  professionalBodyId: string | null = null;

  // Form configuration
  professionalBodyForm!: FormGroup;

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.initializeForm();
    this.checkRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/dashboard' };
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      { label: 'Professional Bodies', routerLink: '/dashboard/admin/training/professional-bodies' },
      { label: this.isUpdateMode ? 'Edit Professional Body' : 'Create Professional Body' },
    ];
  }

  private initializeForm(): void {
    this.professionalBodyForm = this.fb.group({
      professionalBodyName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      professionalBodyUrl: ['', [Validators.required, this.urlValidator]],
      countryBasedOn: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]]
    });
  }

  private urlValidator(control: any) {
    if (!control.value) return null;

    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    const valid = urlPattern.test(control.value);
    return valid ? null : { invalidUrl: true };
  }

  private checkRouteParams(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['edit'] && params['id']) {
        this.isUpdateMode = true;
        this.professionalBodyId = params['id'];
        this.updateBreadcrumbForEdit();
        if (this.professionalBodyId) {
          this.loadProfessionalBodyData(this.professionalBodyId);
        }
      }
    });
  }

  private updateBreadcrumbForEdit(): void {
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      { label: 'Professional Bodies', routerLink: '/dashboard/admin/training/professional-bodies' },
      { label: 'Edit Professional Body' },
    ];
  }

  private loadProfessionalBodyData(id: string): void {
    this.cdr.markForCheck();

    this.professionalBodyService
      .getProfessionalBodyById(id)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data: ProfessionalBody) => {
          this.populateForm(data);
        },
        error: (error: any) => {
          console.error('Error loading professional body data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load professional body data',
          });
          this.router.navigate(['/dashboard/admin/training/professional-bodies']);
        },
      });
  }

  private populateForm(data: ProfessionalBody): void {
    this.professionalBodyForm.patchValue({
      professionalBodyName: data.professionalBodyName,
      professionalBodyUrl: data.professionalBodyUrl,
      countryBasedOn: data.countryBasedOn,
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.professionalBodyForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldErrorMessage(fieldName: string): string {
    const field = this.professionalBodyForm.get(fieldName);
    if (!field || !field.errors) return '';

    const errors = field.errors;

    if (errors['required']) {
      switch (fieldName) {
        case 'professionalBodyName':
          return 'Professional body name is required';
        case 'professionalBodyUrl':
          return 'Website URL is required';
        case 'countryBasedOn':
          return 'Country is required';
        default:
          return 'This field is required';
      }
    }

    if (errors['minlength']) {
      switch (fieldName) {
        case 'professionalBodyName':
          return 'Name must be at least 2 characters long';
        case 'countryBasedOn':
          return 'Country must be at least 2 characters long';
        default:
          return `Minimum length is ${errors['minlength'].requiredLength} characters`;
      }
    }

    if (errors['maxlength']) {
      switch (fieldName) {
        case 'professionalBodyName':
          return 'Name cannot exceed 100 characters';
        case 'countryBasedOn':
          return 'Country cannot exceed 50 characters';
        default:
          return `Maximum length is ${errors['maxlength'].requiredLength} characters`;
      }
    }

    if (errors['invalidUrl']) {
      return 'Please enter a valid URL (e.g., https://example.com)';
    }

    return 'Invalid input';
  }

  onSubmit(): void {
    if (this.professionalBodyForm.valid) {
      this.isSubmitting = true;
      this.cdr.markForCheck();

      const formData = this.professionalBodyForm.value;

      const baseData = {
        professionalBodyName: formData.professionalBodyName!,
        professionalBodyUrl: formData.professionalBodyUrl!,
        countryBasedOn: formData.countryBasedOn!,
      };

      const operation = this.isUpdateMode
        ? this.professionalBodyService.updateProfessionalBody({
            ...baseData,
            professionalBodyId: this.professionalBodyId!
          })
        : this.professionalBodyService.createProfessionalBody(baseData);

      operation
        .pipe(
          takeUntil(this.destroy$),
          finalize(() => {
            this.isSubmitting = false;
            this.cdr.markForCheck();
          })
        )
        .subscribe({
          next: (response) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: this.isUpdateMode
                ? 'Professional body updated successfully'
                : 'Professional body created successfully',
            });

            setTimeout(() => {
              this.router.navigate(['/admin/training/professional-body-managemant']);
            }, 500);
          },
          error: (error) => {
            console.error('Error submitting professional body:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: this.isUpdateMode
                ? 'Failed to update professional body'
                : 'Failed to create professional body',
            });
          },
        });
    } else {
      this.markFormGroupTouched(this.professionalBodyForm);
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly',
      });
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  onCancel(): void {
    if (this.professionalBodyForm.dirty) {
      this.confirmationService.confirm({
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        header: 'Confirm Cancel',
        icon: 'pi pi-exclamation-triangle',
        acceptButtonStyleClass: 'p-button-danger',
        rejectButtonStyleClass: 'p-button-secondary',
        accept: () => {
          this.navigateBack();
        },
      });
    } else {
      this.navigateBack();
    }
  }

  private navigateBack(): void {
    this.router.navigate(['/admin/training/professional-body-managemant']);
  }
}
