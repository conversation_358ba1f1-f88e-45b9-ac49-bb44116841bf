/* Professional Bodies List Component Styles */

/* Table hover effects */
:host ::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
  background-color: #f9fafb;
}

/* Custom table styling */
:host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
  background-color: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
}

:host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  border-bottom: 1px solid #f3f4f6;
  padding: 12px;
}

/* Breadcrumb styling within gradient header */
:host ::ng-deep .p-breadcrumb {
  background: transparent;
  border: none;
}

:host ::ng-deep .p-breadcrumb .p-breadcrumb-list {
  background: transparent;
}

:host ::ng-deep .p-breadcrumb .p-menuitem-link {
  color: rgba(255, 255, 255, 0.9);
}

:host ::ng-deep .p-breadcrumb .p-menuitem-link:hover {
  color: white;
}

:host ::ng-deep .p-breadcrumb .p-menuitem-separator {
  color: rgba(255, 255, 255, 0.7);
}

/* Button hover effects */
:host ::ng-deep .p-button:hover {
  transform: translateY(-1px);
  transition: all 0.2s;
}

/* Action button styling */
:host ::ng-deep .p-button-text:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

:host ::ng-deep .p-button-danger:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

/* Progress spinner styling */
:host ::ng-deep .p-progress-spinner-circle {
  stroke: #2563eb;
}

/* Toast positioning */
:host ::ng-deep .p-toast {
  z-index: 1050;
}

/* Search input styling */
:host ::ng-deep .p-inputtext:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Pagination styling */
:host ::ng-deep .p-paginator {
  background-color: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

:host ::ng-deep .p-paginator .p-paginator-page.p-paginator-page-selected {
  background-color: #3b82f6;
  color: white;
}

/* Empty state styling */
:host ::ng-deep .p-datatable .p-datatable-emptymessage {
  text-align: center;
  padding: 48px 24px;
}

/* Tooltip styling */
:host ::ng-deep .p-tooltip .p-tooltip-text {
  background-color: #1f2937;
  color: white;
  font-size: 12px;
  padding: 6px 10px;
}

/* Summary card icons */
.pi-building {
  color: #3b82f6;
}

.pi-globe {
  color: #10b981;
}

/* Responsive table adjustments */
@media (max-width: 768px) {
  :host ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    padding: 8px;
    font-size: 14px;
  }
}