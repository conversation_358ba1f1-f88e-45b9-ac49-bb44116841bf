import {
  Component,
  OnInit,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { MenuItem, MessageService, ConfirmationService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { ProfessionalBodyService } from '../../../../../services/admin/training/professional-body.service';
import { ProfessionalBody } from '../../../../../models/admin/training/professional-body.model';

@Component({
  selector: 'app-professional-bodies',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TableModule,
    ToastModule,
    ConfirmDialogModule,
    InputTextModule,
    DropdownModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    OverlayPanelModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './professional-bodies.component.html',
  styleUrl: './professional-bodies.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfessionalBodiesComponent implements OnInit, OnDestroy {
  private readonly professionalBodyService = inject(ProfessionalBodyService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();

  // Component state
  professionalBodies: ProfessionalBody[] = [];
  isLoading = false;
  globalFilter = '';

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.loadProfessionalBodies();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/dashboard' };
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      { label: 'Professional Bodies' },
    ];
  }

  loadProfessionalBodies(): void {
    this.isLoading = true;
    this.cdr.markForCheck();

    this.professionalBodyService
      .getAllProfessionalBodies()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoading = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data: ProfessionalBody[]) => {
          this.professionalBodies = data;
          this.cdr.markForCheck();
        },
        error: (error: any) => {
          console.error('Error loading professional bodies:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load professional bodies',
          });
        },
      });
  }

  onCreateNew(): void {
    this.router.navigate(['/admin/training/create-professional-body']);
  }

  onEdit(professionalBody: ProfessionalBody): void {
    this.router.navigate(['/admin/training/create-professional-body'], {
      queryParams: { edit: true, id: professionalBody.professionalBodyId },
    });
  }

  onDelete(professionalBody: ProfessionalBody): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete "${professionalBody.professionalBodyName}"? This action cannot be undone.`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-secondary',
      accept: () => {
        this.deleteProfessionalBody(professionalBody.professionalBodyId);
      },
    });
  }

  private deleteProfessionalBody(id: string): void {
    this.professionalBodyService
      .deleteProfessionalBody(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Professional body deleted successfully',
          });
          this.loadProfessionalBodies();
        },
        error: (error: any) => {
          console.error('Error deleting professional body:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to delete professional body',
          });
        },
      });
  }

  onGlobalFilter(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.globalFilter = target.value;
  }

  openUrl(url: string): void {
    if (url) {
      let formattedUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        formattedUrl = `https://${url}`;
      }
      window.open(formattedUrl, '_blank', 'noopener,noreferrer');
    }
  }

  formatUrl(url: string): string {
    if (!url) return '';
    return url.replace(/^https?:\/\//, '').replace(/^www\./, '');
  }
}
