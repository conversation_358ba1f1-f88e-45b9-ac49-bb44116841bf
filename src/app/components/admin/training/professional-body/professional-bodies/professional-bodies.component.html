<p-toast></p-toast>
<p-confirmDialog></p-confirmDialog>

<!-- Header Section -->
<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-2xl sm:text-3xl">Professional Bodies Management</span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <!-- Professional Bodies Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Table Header Actions -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-building text-blue-600"></i>
                        Professional Bodies List
                    </h3>
                    <p class="text-sm text-gray-600">Manage professional bodies and their details</p>
                </div>

                <div class="flex flex-col sm:flex-row gap-3 sm:items-center">
                    <!-- Create Button -->
                    <p-button icon="pi pi-plus" label="Create Professional Body" styleClass="p-button-primary"
                        (onClick)="onCreateNew()">
                    </p-button>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <p-table [value]="professionalBodies" [paginator]="true" [rows]="10" [rowsPerPageOptions]="[10, 25, 50]"
            dataKey="professionalBodyId" responsiveLayout="scroll" [scrollable]="true" scrollHeight="500px"
            styleClass="p-datatable-sm">

            <!-- Table Header -->
            <ng-template pTemplate="header">
                <tr>
                    <th scope="col" style="width: 35%">
                        Professional Body Name
                    </th>
                    <th scope="col" style="width: 30%">Website</th>
                    <th scope="col" style="width: 20%">Country</th>
                    <th scope="col" style="width: 15%" class="text-center">Actions</th>
                </tr>
            </ng-template>

            <!-- Table Body -->
            <ng-template pTemplate="body" let-professionalBody>
                <tr class="hover:bg-gray-50 transition-colors">
                    <!-- Professional Body Name -->
                    <td>
                        <div class="flex items-center gap-3">
                            <div class="min-w-0 flex-1">
                                <p class="font-medium text-gray-900 truncate">
                                    {{ professionalBody.professionalBodyName }}
                                </p>
                            </div>
                        </div>
                    </td>

                    <!-- Website URL -->
                    <td>
                        <div class="flex items-center gap-2">
                            <span class="text-gray-900 truncate">{{ formatUrl(professionalBody.professionalBodyUrl)
                                }}</span>
                            <p-button *ngIf="professionalBody.professionalBodyUrl" icon="pi pi-external-link"
                                styleClass="p-button-text p-button-sm p-button-rounded" pTooltip="Visit website"
                                tooltipPosition="top" (onClick)="openUrl(professionalBody.professionalBodyUrl)">
                            </p-button>
                        </div>
                    </td>

                    <!-- Country -->
                    <td>
                        <div class="flex items-center gap-2">
                            <i class="pi pi-map-marker text-gray-400"></i>
                            <span class="text-gray-900">{{ professionalBody.countryBasedOn }}</span>
                        </div>
                    </td>

                    <!-- Actions -->
                    <td class="text-center">
                        <div class="flex items-center justify-center gap-1">
                            <!-- Edit Button -->
                            <p-button icon="pi pi-pencil" styleClass="p-button-text p-button-sm p-button-rounded"
                                pTooltip="Edit professional body" tooltipPosition="top"
                                (onClick)="onEdit(professionalBody)">
                            </p-button>

                            <!-- Delete Button -->
                            <p-button icon="pi pi-trash"
                                styleClass="p-button-text p-button-sm p-button-rounded p-button-danger"
                                pTooltip="Delete professional body" tooltipPosition="top"
                                (onClick)="onDelete(professionalBody)">
                            </p-button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <!-- Empty State -->
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="4" class="text-center py-12">
                        <div class="flex flex-col items-center gap-4">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="pi pi-building text-gray-400 text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-1">No Professional Bodies Found</h3>
                                <p-button *ngIf="!globalFilter" icon="pi pi-plus" label="Create Professional Body"
                                    styleClass="p-button-primary" (onClick)="onCreateNew()">
                                </p-button>
                                <p-button *ngIf="globalFilter" icon="pi pi-times" label="Clear Search"
                                    styleClass="p-button-outlined" (onClick)="globalFilter = ''">
                                </p-button>
                            </div>
                        </div>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>