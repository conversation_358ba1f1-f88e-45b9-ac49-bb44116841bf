/* Custom styling for training organization details component */

.organization-section {
  margin-bottom: 1.5rem;
}

.organization-section h3 {
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.document-item {
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.document-item:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.rejection-modal textarea {
  resize: vertical;
  min-height: 80px;
}

.rejection-modal textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
  border-color: #3b82f6;
}

/* Action buttons spacing */
.action-buttons {
  gap: 0.75rem;
}

/* Status tag styling */
.status-tag {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Information grid responsive layout */
@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Document list styling */
.document-list {
  max-height: 400px;
  overflow-y: auto;
}

.document-icon {
  flex-shrink: 0;
}

/* Loading state */
.loading-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Notes section styling */
.notes-section {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-left: 4px solid #3b82f6;
}