<p-toast></p-toast>

<!-- Header Section -->
<div class="w-full px-4 py-4">
  <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
    <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
      <span class="text-white font-semibold text-2xl sm:text-3xl">
        Organization Review
      </span>
      <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">

  <!-- Back Navigation -->
  <div class="mb-6">
    <p-button icon="pi pi-arrow-left" label="Back to Approvals" styleClass="p-button-outlined p-button-sm"
      (onClick)="navigateBack()">
    </p-button>
  </div>

  <!-- Organization Details -->
  <div *ngIf="organizationDetails" class="space-y-6">

    <!-- Header Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex-1">
          <div class="flex flex-col sm:flex-row sm:items-center gap-3">
            <h1 class="text-2xl font-bold text-gray-900">{{ organizationDetails.organizationName }}</h1>
            <p-tag [value]="(organizationDetails.approvalStatus || 'pending') | titlecase"
              [severity]="getStatusSeverity(organizationDetails.approvalStatus || 'pending')" icon="pi pi-clock">
            </p-tag>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0"
          *ngIf="organizationDetails.approvalStatus === 'pending'">
          <p-button icon="pi pi-times" label="Reject" styleClass="p-button-outlined p-button-danger"
            [disabled]="isProcessing" (onClick)="onReject()">
          </p-button>
          <p-button icon="pi pi-check" label="Approve" severity="primary" [disabled]="isProcessing"
            [loading]="isProcessing" (onClick)="onApprove()">
          </p-button>
        </div>
      </div>
    </div>

    <!-- Organization Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <i class="pi pi-building text-blue-600"></i>
        Organization Information
      </h3>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Organization Name</span>
          <p class="text-gray-900">{{ organizationDetails.organizationName }}</p>
        </div>

        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Address</span>
          <p class="text-gray-900">{{ organizationDetails.address }}</p>
        </div>

        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Training Schema</span>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            {{ organizationDetails.trainingSchema }}
          </span>
        </div>

        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Category</span>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
            {{ organizationDetails.category }}
          </span>
        </div>


      </div>
    </div>

    <!-- Contact Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <i class="pi pi-phone text-blue-600"></i>
        Contact Information
      </h3>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Contact Number</span>
          <p class="text-gray-900">{{ organizationDetails.contactNumber }}</p>
        </div>

        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Organization Email</span>
          <p class="text-gray-900">{{ organizationDetails.organizationEmail }}</p>
        </div>
      </div>
    </div>

    <!-- Organization Qualifications -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6"
      *ngIf="organizationDetails.organizationQualification && organizationDetails.organizationQualification.length > 0">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <i class="pi pi-users text-blue-600"></i>
        Organization Qualifications
      </h3>

      <div class="space-y-4">
        <div *ngFor="let qualification of organizationDetails.organizationQualification; let i = index"
          class="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <h4 class="font-semibold text-gray-800 mb-3">Qualification {{ i + 1 }}</h4>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span class="block text-sm font-medium text-gray-700 mb-1">Qualification</span>
              <p class="text-gray-900">{{ qualification.qualificationId }}</p>
            </div>

            <div>
              <span class="block text-sm font-medium text-gray-700 mb-1">Employee Name</span>
              <p class="text-gray-900">{{ qualification.employeeName }}</p>
            </div>

            <div>
              <span class="block text-sm font-medium text-gray-700 mb-1">Employee Designation</span>
              <p class="text-gray-900">{{ qualification.employeeDesignation }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Submission Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <i class="pi pi-info-circle text-blue-600"></i>
        Submission Information
      </h3>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Registered By</span>
          <p class="text-gray-900">{{ organizationDetails.registeredBy }}</p>
        </div>

        <div>
          <span class="block text-sm font-medium text-gray-700 mb-1">Current Status</span>
          <p-tag [value]="(organizationDetails.approvalStatus || 'pending') | titlecase"
            [severity]="getStatusSeverity(organizationDetails.approvalStatus || 'pending')" icon="pi pi-clock">
          </p-tag>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Rejection Modal -->
<p-dialog [(visible)]="showRejectionModal" header="Reject Organization Application" [modal]="true" [closable]="true"
  [resizable]="false" [style]="{ width: '500px' }" [dismissableMask]="false">

  <form [formGroup]="rejectionForm" (ngSubmit)="onConfirmRejection()">
    <div class="space-y-4">
      <!-- Reason Field -->
      <div>
        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
          Rejection Reason <span class="text-red-500">*</span>
        </label>
        <textarea id="reason" formControlName="reason" rows="3"
          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Provide a clear reason for rejection..." [class.border-red-500]="isFieldInvalid('reason')">
        </textarea>
        <small *ngIf="isFieldInvalid('reason')" class="text-red-500">
          <span *ngIf="rejectionForm.get('reason')?.errors?.['required']">
            Rejection reason is required
          </span>
        </small>
      </div>
    </div>

    <!-- Modal Actions -->
    <div class="flex flex-col sm:flex-row gap-3 sm:justify-end mt-6">
      <p-button type="button" icon="pi pi-times" label="Cancel" styleClass="p-button-outlined" [disabled]="isProcessing"
        (onClick)="onCancelRejection()">
      </p-button>

      <p-button type="submit" icon="pi pi-check" label="Confirm Rejection" styleClass="p-button-danger"
        [disabled]="rejectionForm.invalid || isProcessing" [loading]="isProcessing">
      </p-button>
    </div>
  </form>
</p-dialog>

<!-- Approval Modal -->
<p-dialog [(visible)]="showApprovalModal" header="Approve Organization" [modal]="true" [closable]="true"
  [resizable]="false" [style]="{ width: '600px' }" [dismissableMask]="false">

  <form [formGroup]="approvalForm" (ngSubmit)="onConfirmApproval()">
    <div class="space-y-6">
      <!-- Organization Info -->
      <div>
        <div class="flex items-center gap-3 mb-4">
          <i class="pi pi-building text-blue-600 text-lg"></i>
          <h2 class="text-xl font-bold text-blue-900 mb-3">{{ organizationDetails?.organizationName }}</h2>
        </div>
      </div>

      <!-- Training Schema Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Training Schema <span class="text-red-500">*</span>
        </label>
        <p class="text-sm text-gray-600 mb-4">
          Please select the appropriate training schema for this organization:
        </p>

        <div class="space-y-3">
          <!-- Monitored Option -->
          <div class="flex items-start gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
            [class.border-blue-500]="approvalForm.get('trainingSchema')?.value === 'Monitored'"
            [class.bg-blue-50]="approvalForm.get('trainingSchema')?.value === 'Monitored'"
            (click)="approvalForm.patchValue({ trainingSchema: 'Monitored' })">
            <input type="radio" id="monitored" formControlName="trainingSchema" value="Monitored"
              class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
            <div class="flex-1">
              <label for="monitored" class="font-medium text-gray-900 cursor-pointer">
                Monitored
              </label>
              <p class="text-sm text-gray-600 mt-1">
                Regular oversight and monitoring of training activities. Requires periodic reviews and compliance
                checks.
              </p>
            </div>
          </div>

          <!-- Non-Monitored Option -->
          <div class="flex items-start gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
            [class.border-blue-500]="approvalForm.get('trainingSchema')?.value === 'Non-Monitored'"
            [class.bg-blue-50]="approvalForm.get('trainingSchema')?.value === 'Non-Monitored'"
            (click)="approvalForm.patchValue({ trainingSchema: 'Non-Monitored' })">
            <input type="radio" id="non-monitored" formControlName="trainingSchema" value="Non-Monitored"
              class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
            <div class="flex-1">
              <label for="non-monitored" class="font-medium text-gray-900 cursor-pointer">
                Non-Monitored
              </label>
              <p class="text-sm text-gray-600 mt-1">
                Self-managed training activities with minimal external oversight. Organization maintains full
                responsibility.
              </p>
            </div>
          </div> <!-- Hybrid Option -->
          <div class="flex items-start gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
            [class.border-blue-500]="approvalForm.get('trainingSchema')?.value === 'Hybrid'"
            [class.bg-blue-50]="approvalForm.get('trainingSchema')?.value === 'Hybrid'"
            (click)="approvalForm.patchValue({ trainingSchema: 'Hybrid' })">
            <input type="radio" id="hybrid" formControlName="trainingSchema" value="Hybrid"
              class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
            <div class="flex-1">
              <label for="hybrid" class="font-medium text-gray-900 cursor-pointer">
                Hybrid
              </label>
              <p class="text-sm text-gray-600 mt-1">
                Combination of monitored and non-monitored training approaches. Flexible oversight based on specific
                requirements.
              </p>
            </div>
          </div>
        </div>

        <small *ngIf="isApprovalFieldInvalid('trainingSchema')" class="text-red-500 block mt-2">
          Please select a training schema
        </small>
      </div> <!-- Category Field -->
      <div>
        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
          Category <span class="text-red-500">*</span>
        </label>
        <p-dropdown id="category" formControlName="category" [options]="categoryOptions" optionLabel="label"
          optionValue="value" placeholder="Select organization category" class="w-full"
          [class.ng-invalid]="isApprovalFieldInvalid('category')">
        </p-dropdown>
        <small *ngIf="isApprovalFieldInvalid('category')" class="text-red-500">
          Category is required
        </small>
      </div>

      <!-- Approved Remarks Field -->
      <div>
        <label for="approvedRemarks" class="block text-sm font-medium text-gray-700 mb-2">
          Approval Remarks <span class="text-red-500">*</span>
        </label>
        <textarea id="approvedRemarks" formControlName="approvedRemarks" rows="3"
          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter approval remarks and comments..."
          [class.border-red-500]="isApprovalFieldInvalid('approvedRemarks')">
        </textarea>
        <small *ngIf="isApprovalFieldInvalid('approvedRemarks')" class="text-red-500">
          <span *ngIf="approvalForm.get('approvedRemarks')?.errors?.['required']">
            Approval remarks are required
          </span>
          <span *ngIf="approvalForm.get('approvedRemarks')?.errors?.['minlength']">
            Remarks must be at least 5 characters long
          </span>
        </small>
      </div>

      <!-- Confirmation Notice -->
      <div class="flex items-start gap-3">
        <i class="pi pi-check-circle text-green-600 text-lg mt-0.5"></i>
        <div>
          <h5 class="font-medium text-green-900 mb-1">Approval Confirmation</h5>
          <p class="text-sm text-green-800">
            By approving this organization, you confirm that all documentation has been reviewed and the organization
            meets the required standards for the selected training schema.
          </p>
        </div>
      </div>
    </div>

    <!-- Modal Actions -->
    <div class="flex flex-col sm:flex-row gap-3 sm:justify-end mt-6">
      <p-button type="button" icon="pi pi-times" label="Cancel" outlined="true" severity="danger"
        [disabled]="isProcessing" (onClick)="onCancelApproval()">
      </p-button>

      <p-button type="submit" icon="pi pi-check" label="Confirm Approval" severity="primary"
        [disabled]="approvalForm.invalid || isProcessing" [loading]="isProcessing">
      </p-button>
    </div>
  </form>
</p-dialog>