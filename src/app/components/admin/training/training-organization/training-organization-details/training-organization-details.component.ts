import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { MenuItem, MessageService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { TrainingOrganizationApprovalService } from '../../../../../services/admin/training/training-organization-approval.service';
import { AuthService } from '../../../../../services/auth/auth.service';

@Component({
  selector: 'app-training-organization-details',  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    CardModule,
    TagModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    DialogModule,
    DividerModule,
    DropdownModule,
  ],
  providers: [MessageService],
  templateUrl: './training-organization-details.component.html',
  styleUrl: './training-organization-details.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingOrganizationDetailsComponent implements OnInit, OnDestroy {
  private readonly approvalService = inject(
    TrainingOrganizationApprovalService
  );
  private readonly messageService = inject(MessageService);
  private readonly _authService = inject(AuthService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly fb = inject(FormBuilder);
  private readonly destroy$ = new Subject<void>();

  isProcessing = false;
  organizationId!: string;
  organizationDetails: any | null = null;

  // Modal states
  showRejectionModal = false;
  showApprovalModal = false;
  // Forms
  rejectionForm!: FormGroup;
  approvalForm!: FormGroup;

  // Category options
  categoryOptions = [
    { label: 'Training Partners', value: 'Training_Partners' },
    { label: 'Training Organizations', value: 'Training_Organizations' },
    { label: 'Government Agencies', value: 'Government_Agencies' },
    { label: 'NGOs', value: 'NGOs' },
    { label: 'Private Companies', value: 'Private_Companies' },
    { label: 'Educational Institutions', value: 'Educational_Institutions' },
    { label: 'Not Defined', value: 'NotDefined' }
  ];

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
  ngOnInit(): void {
    this.organizationId = this.route.snapshot.paramMap.get('id') ?? '';
    this.initializeBreadcrumb();
    this.initializeForms();
    this.loadOrganizationDetails();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  private initializeForms(): void {
    this.rejectionForm = this.fb.group({
      reason: ['', [Validators.required]],
    });    this.approvalForm = this.fb.group({
      trainingSchema: ['', Validators.required],
      category: ['Training_Partners', Validators.required],
      approvedRemarks: ['', [Validators.required]]
    });
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/admin' };
    this.breadcrumbItems = [
      { label: 'Training Management' },
      {
        label: 'Organization Approvals',
        routerLink: '/admin/training/organization-approvals',
      },
      { label: 'Details' },
    ];
  }

  private loadOrganizationDetails(): void {
    if (!this.organizationId) return;

    this.cdr.markForCheck();

    this.approvalService
      .getOrganizationDetailsById(this.organizationId)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (details: any) => {
          // Map the details to TrainingOrganizationResponse format
          this.organizationDetails = {
            pId: details.pId,
            strOrganizationId:
              details.organizationId ?? details.strOrganizationId,
            organizationName: details.name ?? details.organizationName,
            trainingSchema: details.trainingSchema ?? 'Monitored',
            category: details.category,
            address: details.address,
            contactNumber: details.contactNumber,
            organizationEmail: details.organizationEmail,
            caSriLankaRegistrationNumber: details.caSriLankaRegistrationNumber,
            organizationRegisterNumber: details.organizationRegisterNumber,
            computationalEnvironment: details.computationalEnvironment ?? false,
            registeredBy: details.registeredBy,
            createdAt:
              details.submittedAt ?? details.registeredAt ?? details.createdAt,
            updatedAt: details.updatedAt ?? details.createdAt,
            approvalStatus:
              details.status ?? details.approvalStatus ?? 'pending',
            organizationQualification: details.organizationQualification ?? [],
          };
          this.cdr.markForCheck();
        },
        error: (error) => {
          console.error('Error loading organization details:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load organization details',
          });
          setTimeout(() => {
            this.navigateBack();
          }, 500);
        },
      });
  }

  onApprove(): void {
    this.showApprovalModal = true;
    this.approvalForm.reset();
  }

  onReject(): void {
    this.showRejectionModal = true;
    this.rejectionForm.reset();
  }

  onConfirmApproval(): void {
    if (!this.organizationId || this.approvalForm.invalid) {
      this.markFormGroupTouched(this.approvalForm);
      return;
    }

    this.isProcessing = true;
    this.cdr.markForCheck();

    const approvalData = {
      trainingSchema: this.approvalForm.value.trainingSchema,
      category: this.approvalForm.value.category,
      userID: this._authService.getCurrentUser()?.username ?? '',
      approveStatus: 1,
      approvedRemarks: this.approvalForm.value.approvedRemarks
    };

    this.approvalService
      .approveOrganization(this.organizationId, approvalData)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isProcessing = false;
          this.showApprovalModal = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Organization approved successfully',
          });
          setTimeout(() => this.navigateBack(), 2000);
        },
        error: (error) => {
          console.error('Error approving organization:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to approve organization',
          });
        },
      });
  }
  onConfirmRejection(): void {
    if (!this.organizationId || this.rejectionForm.invalid) {
      this.markFormGroupTouched(this.rejectionForm);
      return;
    }

    this.isProcessing = true;
    this.cdr.markForCheck();

    const rejectionData = {
      trainingSchema: 'NotDefined',
      category: 'NotDefined',
      userID: this._authService.getCurrentUser()?.username ?? '',
      approveStatus: 2,
      approvedRemarks: this.rejectionForm.value.reason
    };

    this.approvalService
      .approveOrganization(this.organizationId, rejectionData)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isProcessing = false;
          this.showRejectionModal = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Organization rejected successfully',
          });
          setTimeout(() => this.navigateBack(), 2000);
        },
        error: (error) => {
          console.error('Error rejecting organization:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to reject organization',
          });
        },
      });
  }

  onCancelApproval(): void {
    this.showApprovalModal = false;
    this.approvalForm.reset();
  }

  onCancelRejection(): void {
    this.showRejectionModal = false;
    this.rejectionForm.reset();
  }

  navigateBack(): void {
    this.router.navigate(['/admin/training/organization-approvals']);
  }

  getStatusSeverity(status: string): 'info' | 'warn' | 'success' | 'danger' {
    switch (status) {
      case 'pending':
        return 'warn';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'danger';
      default:
        return 'info';
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.rejectionForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isApprovalFieldInvalid(fieldName: string): boolean {
    const field = this.approvalForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
}
