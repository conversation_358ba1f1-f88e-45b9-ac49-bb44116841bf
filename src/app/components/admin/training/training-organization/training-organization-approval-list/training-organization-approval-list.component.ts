import {
  Compo<PERSON>,
  OnInit,
  OnD<PERSON>roy,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MenuItem, MessageService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { TrainingOrganizationApprovalService } from '../../../../../services/admin/training/training-organization-approval.service';

@Component({
  selector: 'app-training-organization-approval-list',
  imports: [
    CommonModule,
    ButtonModule,
    CardModule,
    TableModule,
    TagModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
  ],
  providers: [MessageService],
  templateUrl: './training-organization-approval-list.component.html',
  styleUrl: './training-organization-approval-list.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingOrganizationApprovalListComponent
  implements OnInit, OnDestroy
{
  private readonly approvalService = inject(
    TrainingOrganizationApprovalService
  );
  private readonly messageService = inject(MessageService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();

  pendingOrganizations: any[] = [];

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  totalRecords: number = 0;
  pageSize: number = 10;
  pageNumber: number = 1;

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.loadPendingOrganizations(this.pageNumber, this.pageSize);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/admin' };
    this.breadcrumbItems = [
      { label: 'Admin Panel' },
      { label: 'Training Management' },
      { label: 'Organization Approvals' },
    ];
  }
  private loadPendingOrganizations(pageNumber: number, pageSize: number): void {
    this.cdr.markForCheck();

    this.approvalService
      .getPendingOrganizations(pageNumber, pageSize)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (response) => {
          this.pendingOrganizations = response.body || [];
          this.cdr.markForCheck();
        },
        error: (error: any) => {
          console.error('Error loading pending organizations:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load pending organizations',
          });
        },
      });
  }

  onViewDetails(organizationId: string): void {
    this.router.navigate([
      '/admin/training/organization-details',
      organizationId,
    ]);
  }
  
  getStatusSeverity(status: string): 'info' | 'warn' | 'success' | 'danger' {
    if (status === 'pending') {
      return 'warn';
    }
    return 'info';
  }
}
