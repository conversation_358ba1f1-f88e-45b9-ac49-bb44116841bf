<p-toast></p-toast>

<!-- Header Section -->
<div class="w-full px-4 py-4">
  <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
    <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
      <span class="text-white font-semibold text-2xl sm:text-3xl">
        Training Organization Approvals
      </span>
      <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">

  <!-- Actions Bar -->
  <div class="flex flex-col sm:flex-row gap-4 sm:justify-between sm:items-center mb-6">
    <div>
      <h2 class="text-xl font-semibold text-gray-900">Pending Approvals</h2>
      <p class="text-sm text-gray-600 mt-1">Review and approve organization registrations</p>
    </div>
  </div>

  <!-- Organizations Table -->
  <div *ngIf="pendingOrganizations.length > 0"
    class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <p-table [value]="pendingOrganizations" [paginator]="true" stripedRows [rows]="10" [showCurrentPageReport]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} organizations"
      [rowsPerPageOptions]="[5, 10, 20]" styleClass="p-datatable-sm" [tableStyle]="{ 'min-width': '50rem' }">
      <!-- Organization Name Column -->
      <ng-template pTemplate="header">
        <tr>
          <th scope="col" class="text-left py-4 px-4 font-semibold text-gray-900">Organization</th>
          <th scope="col" class="text-left py-4 px-4 font-semibold text-gray-900 hidden md:table-cell">Contact Info
          </th>
          <th scope="col" class="text-left py-4 px-4 font-semibold text-gray-900 hidden md:table-cell">Email
          </th>
          <th scope="col" class="text-left py-4 px-4 font-semibold text-gray-900 hidden lg:table-cell">Address</th>
          <th scope="col" class="text-left py-4 px-4 font-semibold text-gray-900">Status</th>
          <th scope="col" class="text-center py-4 px-4 font-semibold text-gray-900">Actions</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-organization>
        <tr class="border-t border-gray-100 hover:bg-gray-50"> <!-- Organization Info -->
          <td class="py-4 px-4">
            <div class="flex flex-col">
              <span class="font-medium text-gray-900">{{ organization.organizationName }}</span>
            </div>
          </td>

          <!-- Contact Info (Hidden on mobile) -->
          <td class="py-4 px-4 hidden md:table-cell">
            <div class="flex flex-col">
              <span class="text-gray-900">{{ organization.contactNumber }}</span>
            </div>
          </td>

          <!-- Email -->
          <td class="py-4 px-4 hidden md:table-cell">
            <div class="flex flex-col">
              <span class="text-gray-900">{{ organization.organizationEmail }}</span>
            </div>
          </td>

          <!-- Address (Hidden on tablet and below) -->
          <td class="py-4 px-4 hidden lg:table-cell">
            <span class="text-gray-900 text-sm">{{ organization.address | slice:0:50 }}{{ organization.address.length >
              50 ? '...' : '' }}</span>
          </td>

          <!-- Status -->
          <td class="py-4 px-4">
            <p-tag [value]="organization.approvedStatus | titlecase"
              [severity]="getStatusSeverity(organization.approvedStatus)">
            </p-tag>
          </td>

          <!-- Actions -->
          <td class="py-4 px-4 text-center">
            <div class="flex justify-center gap-2">
              <p-button icon="pi pi-eye" styleClass="p-button-text p-button-sm p-button-rounded" pTooltip="View Details"
                tooltipPosition="top" (onClick)="onViewDetails(organization.organizationId)">
              </p-button>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State Template -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="text-center py-12">
            <div class="flex flex-col items-center justify-center">
              <i class="pi pi-inbox text-4xl text-gray-400 mb-4"></i>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No pending approvals</h3>
              <p class="text-gray-500 text-sm">All organization registrations have been processed.</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>

  <!-- Empty State (No Data) -->
  <div *ngIf="pendingOrganizations.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
    <div class="text-center">
      <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
        <i class="pi pi-inbox text-2xl text-gray-500"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No pending approvals</h3>
      <p class="text-gray-500 mb-6">There are currently no training organization registrations waiting for approval.</p>
    </div>
  </div>
</div>