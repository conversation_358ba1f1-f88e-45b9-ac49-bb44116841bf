/* Custom styling for training organization approval list component */

.p-datatable .p-datatable-tbody > tr {
  transition: background-color 0.2s ease;
}

.p-datatable .p-datatable-tbody > tr:hover {
  background-color: #f9fafb !important;
}

.p-button-rounded {
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .p-datatable .p-datatable-tbody > tr > td {
    padding: 1rem 0.75rem;
  }
  
  .p-datatable .p-datatable-thead > tr > th {
    padding: 1rem 0.75rem;
  }
}

/* Statistics cards hover effect */
.bg-white:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease;
}

/* Empty state styling */
.empty-state-icon {
  color: #9ca3af;
  font-size: 3rem;
}