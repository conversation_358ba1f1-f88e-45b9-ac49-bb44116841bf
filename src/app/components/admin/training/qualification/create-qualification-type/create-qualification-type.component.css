/* Custom styles for create qualification type component */

.custom-spinner {
  color: #198BDB;
}

.p-inputtext:focus,
.p-inputtextarea:focus {
  border-color: #198BDB;
  box-shadow: 0 0 0 0.2rem rgba(25, 139, 219, 0.2);
}

.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
  border-color: #198BDB;
}

.p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight {
  background-color: #198BDB;
  border-color: #198BDB;
}

.p-button.p-button-primary {
  background-color: #198BDB;
  border-color: #198BDB;
}

.p-button.p-button-primary:hover {
  background-color: #1976B2;
  border-color: #1976B2;
}

.p-button.p-button-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(25, 139, 219, 0.2);
}

.p-progressspinner .p-progressspinner-circle {
  stroke: #198BDB;
}

/* Form validation styles */
.ng-invalid.ng-touched,
.ng-invalid.ng-dirty {
  border-color: #dc2626;
}

.ng-invalid.ng-touched:focus,
.ng-invalid.ng-dirty:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.2);
}

/* Responsive design */
@media (max-width: 640px) {
  .p-button {
    width: 100%;
    justify-content: center;
  }
  
  .bg-gradient-to-r {
    padding: 1rem;
  }
  
  .text-2xl {
    font-size: 1.5rem;
  }
}