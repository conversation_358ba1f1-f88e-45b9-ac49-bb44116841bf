import {
  Component,
  On<PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TextareaModule } from 'primeng/textarea';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MenuItem, MessageService, ConfirmationService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { QualificationTypeService } from '../../../../../services/admin/training/qualification-type.service';
import { QualificationType } from '../../../../../models/admin/training/qualification-type.model';

@Component({
  selector: 'app-create-qualification-type',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    TextareaModule,
    CheckboxModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './create-qualification-type.component.html',
  styleUrl: './create-qualification-type.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateQualificationTypeComponent implements OnInit, OnDestroy {
  private readonly fb = inject(FormBuilder);
  private readonly qualificationTypeService = inject(QualificationTypeService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly destroy$ = new Subject<void>();

  // Component state
  isSubmitting = false;
  isUpdateMode = false;
  qualificationTypeId!: string;

  // Form configuration
  qualificationTypeForm!: FormGroup;

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.initializeForm();
    this.checkRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/dashboard' };
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      {
        label: 'Qualification Types',
        routerLink: '/dashboard/admin/training/qualification-types',
      },
      {
        label: this.isUpdateMode
          ? 'Edit Qualification Type'
          : 'Create Qualification Type',
      },
    ];
  }

  private initializeForm(): void {
    this.qualificationTypeForm = this.fb.group({
      qualificationTypeName: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(100),
        ],
      ],
      qualificationTypeDescription: [
        '',
        [
          Validators.required,
          Validators.minLength(10),
          Validators.maxLength(500),
        ],
      ],
      isActive: [true, Validators.required],
    });
  }

  private checkRouteParams(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['edit'] && params['id']) {
        this.isUpdateMode = true;
        this.qualificationTypeId = params['id'];
        this.updateBreadcrumbForEdit();
        if (this.qualificationTypeId) {
          this.loadQualificationTypeData(this.qualificationTypeId);
        }
      }
    });
  }

  private updateBreadcrumbForEdit(): void {
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      {
        label: 'Qualification Types',
        routerLink: '/dashboard/admin/training/qualification-types',
      },
      { label: 'Edit Qualification Type' },
    ];
  }

  private loadQualificationTypeData(id: string): void {
    this.cdr.markForCheck();

    this.qualificationTypeService
      .getQualificationTypeById(id)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data: QualificationType) => {
          this.populateForm(data);
        },
        error: (error: any) => {
          console.error('Error loading qualification type data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load qualification type data',
          });
        },
      });
  }

  private populateForm(data: QualificationType): void {
    this.qualificationTypeForm.patchValue({
      qualificationTypeName: data.qualificationTypeName,
      qualificationTypeDescription: data.qualificationTypeDescription,
      isActive: data.isActive,
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.qualificationTypeForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldErrorMessage(fieldName: string): string {
    const field = this.qualificationTypeForm.get(fieldName);
    if (!field?.errors) return '';

    const errors = field.errors;

    if (errors['required']) {
      switch (fieldName) {
        case 'qualificationTypeName':
          return 'Qualification type name is required';
        case 'qualificationTypeDescription':
          return 'Description is required';
        default:
          return 'This field is required';
      }
    }

    if (errors['minlength']) {
      switch (fieldName) {
        case 'qualificationTypeName':
          return 'Name must be at least 2 characters long';
        case 'qualificationTypeDescription':
          return 'Description must be at least 10 characters long';
        default:
          return `Minimum length is ${errors['minlength'].requiredLength} characters`;
      }
    }

    if (errors['maxlength']) {
      switch (fieldName) {
        case 'qualificationTypeName':
          return 'Name cannot exceed 100 characters';
        case 'qualificationTypeDescription':
          return 'Description cannot exceed 500 characters';
        default:
          return `Maximum length is ${errors['maxlength'].requiredLength} characters`;
      }
    }

    return 'Invalid input';
  }

  onSubmit(): void {
    if (this.qualificationTypeForm.valid) {
      this.isSubmitting = true;
      this.cdr.markForCheck();

      const formData = this.qualificationTypeForm.value;
      const qualificationTypeData: QualificationType = this.isUpdateMode
        ? {
            qualificationTypeId: this.qualificationTypeId,
            qualificationTypeName: formData.qualificationTypeName,
            qualificationTypeDescription: formData.qualificationTypeDescription,
            isActive: formData.isActive,
          }
        : ({
            qualificationTypeName: formData.qualificationTypeName,
            qualificationTypeDescription: formData.qualificationTypeDescription,
            isActive: formData.isActive,
          } as QualificationType);

      const operation = this.isUpdateMode
        ? this.qualificationTypeService.updateQualificationType(
            qualificationTypeData
          )
        : this.qualificationTypeService.createQualificationType(
            qualificationTypeData
          );

      operation
        .pipe(
          takeUntil(this.destroy$),
          finalize(() => {
            this.isSubmitting = false;
            this.cdr.markForCheck();
          })
        )
        .subscribe({
          next: (response) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: this.isUpdateMode
                ? 'Qualification type updated successfully'
                : 'Qualification type created successfully',
            });

            setTimeout(() => {
              this.router.navigate([
                '/admin/training/qualification-type-managemant',
              ]);
            }, 500);
          },
          error: (error) => {
            console.error('Error submitting qualification type:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: this.isUpdateMode
                ? 'Failed to update qualification type'
                : 'Failed to create qualification type',
            });
          },
        });
    } else {
      this.markFormGroupTouched(this.qualificationTypeForm);
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly',
      });
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  onCancel(): void {
    if (this.qualificationTypeForm.dirty) {
      this.confirmationService.confirm({
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        header: 'Confirm Cancel',
        icon: 'pi pi-exclamation-triangle',
        acceptButtonStyleClass: 'p-button-danger',
        rejectButtonStyleClass: 'p-button-secondary',
        accept: () => {
          this.navigateBack();
        },
      });
    } else {
      this.navigateBack();
    }
  }

  private navigateBack(): void {
    this.router.navigate(['/admin/training/qualification-type-managemant']);
  }
}
