<p-toast></p-toast>
<p-confirmDialog></p-confirmDialog>

<!-- Header Section -->
<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-2xl sm:text-3xl">
                {{ isUpdateMode ? 'Edit Qualification Type' : 'Create Qualification Type' }}
            </span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <div class="max-w-3xl mx-auto">
        <form [formGroup]="qualificationTypeForm" (ngSubmit)="onSubmit()">

            <!-- Basic Information Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-bookmark text-blue-600"></i>
                        Basic Information
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">
                        {{ isUpdateMode ? 'Update the qualification type details' : 'Provide basic information for the new qualification type' }}
                    </p>
                </div>

                <div class="space-y-6">
                    <!-- Qualification Type Name -->
                    <div>
                        <label for="qualificationTypeName" class="block text-sm font-medium text-gray-700 mb-2">
                            Qualification Type Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="qualificationTypeName" pInputText formControlName="qualificationTypeName"
                            class="w-full h-11"
                            placeholder="Enter qualification type name (e.g., Professional Accounting, Management)"
                            [class.ng-invalid]="isFieldInvalid('qualificationTypeName')" maxlength="100">
                        <small *ngIf="isFieldInvalid('qualificationTypeName')" class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('qualificationTypeName') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            {{ qualificationTypeForm.get('qualificationTypeName')?.value?.length || 0 }}/100 characters
                        </small>
                    </div>

                    <!-- Qualification Type Description -->
                    <div>
                        <label for="qualificationTypeDescription" class="block text-sm font-medium text-gray-700 mb-2">
                            Description <span class="text-red-500">*</span>
                        </label>
                        <textarea id="qualificationTypeDescription" pTextarea
                            formControlName="qualificationTypeDescription" rows="4" class="w-full"
                            placeholder="Provide a detailed description of this qualification type, including what types of qualifications fall under this category"
                            [class.ng-invalid]="isFieldInvalid('qualificationTypeDescription')" maxlength="500">
                        </textarea>
                        <small *ngIf="isFieldInvalid('qualificationTypeDescription')" class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('qualificationTypeDescription') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            {{ qualificationTypeForm.get('qualificationTypeDescription')?.value?.length || 0 }}/500
                            characters
                        </small>
                    </div>
                </div>
            </div>

            <!-- Status Configuration Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-cog text-blue-600"></i>
                        Status Configuration
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">Configure the availability status of this qualification type
                    </p>
                </div>

                <!-- Active Status -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <div class="flex items-start gap-3">
                        <p-checkbox formControlName="isActive" [binary]="true" inputId="isActive"
                            [class.ng-invalid]="isFieldInvalid('isActive')">
                        </p-checkbox>
                        <div class="flex-1">
                            <label for="isActive" class="text-sm font-medium text-gray-900 cursor-pointer">
                                Active Status
                            </label>
                            <p class="text-sm text-gray-600 mt-1">
                                When active, this qualification type will be available for selection when creating
                                qualifications.
                                Inactive qualification types are hidden from users but preserved in the system.
                            </p>
                            <div class="mt-2">
                                <span *ngIf="qualificationTypeForm.get('isActive')?.value"
                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="pi pi-check-circle mr-1"></i>
                                    Active
                                </span>
                                <span *ngIf="!qualificationTypeForm.get('isActive')?.value"
                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="pi pi-times-circle mr-1"></i>
                                    Inactive
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                    <!-- Cancel Button -->
                    <p-button type="button" icon="pi pi-times" label="Cancel"
                        styleClass="p-button-outlined w-full sm:w-auto" (onClick)="onCancel()"
                        [disabled]="isSubmitting">
                    </p-button>

                    <!-- Submit Button -->
                    <p-button type="submit" icon="pi pi-check"
                        [label]="isUpdateMode ? 'Update Qualification Type' : 'Create Qualification Type'"
                        [disabled]="qualificationTypeForm.invalid || isSubmitting" [loading]="isSubmitting"
                        styleClass="p-button-primary w-full sm:w-auto" [style]="{ 'min-width': '200px' }">
                    </p-button>
                </div>

                <!-- Form Status Message -->
                <div *ngIf="qualificationTypeForm.invalid && qualificationTypeForm.touched"
                    class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center gap-2">
                        <i class="pi pi-exclamation-triangle text-red-600"></i>
                        <p class="text-sm text-red-800">
                            Please fill in all required fields correctly before submitting.
                        </p>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>