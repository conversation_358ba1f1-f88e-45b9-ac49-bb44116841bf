import {
  Component,
  On<PERSON>ni<PERSON>,
  On<PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { TooltipModule } from 'primeng/tooltip';
import { MenuItem, MessageService, ConfirmationService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { QualificationType } from '../../../../../models/admin/training/qualification-type.model';
import { QualificationTypeService } from '../../../../../services/admin/training/qualification-type.service';

@Component({
  selector: 'app-qualification-types',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TableModule,
    TagModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    ConfirmDialogModule,
    TooltipModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './qualification-types.component.html',
  styleUrl: './qualification-types.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QualificationTypesComponent implements OnInit, OnDestroy {
  private readonly qualificationTypeService = inject(QualificationTypeService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();

  // Component state
  qualificationTypes: QualificationType[] = [];
  isPerformingAction = false;

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.loadQualificationTypes();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/dashboard' };
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      { label: 'Qualification Types' },
    ];
  }

  private loadQualificationTypes(): void {
    this.cdr.markForCheck();

    this.qualificationTypeService
      .getAllQualificationTypes()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data: QualificationType[]) => {
          this.qualificationTypes = data;
        },
        error: (error: any) => {
          console.error('Error loading qualification types:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load qualification types',
          });
        },
      });
  }

  onCreateNew(): void {
    this.router.navigate(['/admin/training/create-qualification-type']);
  }

  onEdit(qualificationType: QualificationType): void {
    this.router.navigate(['/admin/training/create-qualification-type'], {
      queryParams: { edit: true, id: qualificationType.qualificationTypeId },
    });
  }

  onDelete(qualificationTypeId: string): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete? This action cannot be undone.`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-secondary',
      accept: () => {
        this.performDelete(qualificationTypeId);
      },
    });
  }

  private performDelete(qualificationTypeId: string): void {
    this.isPerformingAction = true;
    this.cdr.markForCheck();

    this.qualificationTypeService
      .deleteQualificationType(qualificationTypeId)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isPerformingAction = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Qualification type deleted successfully',
          });
        },
        complete: () => {
          this.loadQualificationTypes();
        },
        error: (error) => {
          console.error('Error deleting qualification type:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to delete qualification type',
          });
        },
      });
  }

  getStatusSeverity(isActive: boolean): 'success' | 'danger' {
    return isActive ? 'success' : 'danger';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  getStatusIcon(isActive: boolean): string {
    return isActive ? 'pi-check-circle' : 'pi-times-circle';
  }
}
