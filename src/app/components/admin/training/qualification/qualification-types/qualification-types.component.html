<p-toast></p-toast>
<p-confirmDialog></p-confirmDialog>

<!-- Header Section -->
<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-2xl sm:text-3xl">
                Qualification Types Management
            </span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <!-- Actions Bar -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h2 class="text-lg font-semibold text-gray-900">Qualification Types</h2>
                <p class="text-sm text-gray-600 mt-1">
                    Manage qualification type categories for training organization registrations
                </p>
            </div>
            <div class="flex gap-2">
                <p-button icon="pi pi-plus" label="Create New Type" styleClass="p-button-primary"
                    (onClick)="onCreateNew()" [disabled]="isPerformingAction">
                </p-button>
            </div>
        </div>
    </div>

    <!-- Qualification Types Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Empty State -->
        <div *ngIf="qualificationTypes.length === 0" class="flex flex-col items-center justify-center py-12 px-4">
            <div class="text-center max-w-md">
                <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="pi pi-bookmark text-2xl text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Qualification Types Found</h3>
                <p class="text-gray-600 mb-6">
                    Get started by creating your first qualification type. This will help categorize different types of
                    professional qualifications.
                </p>
                <p-button icon="pi pi-plus" label="Create First Type" styleClass="p-button-primary"
                    (onClick)="onCreateNew()">
                </p-button>
            </div>
        </div>

        <!-- Data Table -->
        <p-table *ngIf="qualificationTypes.length > 0" [value]="qualificationTypes" styleClass="p-datatable-sm"
            [paginator]="qualificationTypes.length > 10" [rows]="10" [showCurrentPageReport]="true"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} qualification types">
            <!-- Table Header -->
            <ng-template pTemplate="header">
                <tr>
                    <th scope="col" style="width: 30%">
                        Qualification Type
                    </th>
                    <th scope="col" style="width: 45%">Description</th>
                    <th scope="col" style="width: 10%" class="text-center">Status</th>
                    <th scope="col" style="width: 15%" class="text-center">Actions</th>
                </tr>
            </ng-template>

            <!-- Table Body -->
            <ng-template pTemplate="body" let-qualificationType let-rowIndex="rowIndex">
                <tr class="hover:bg-gray-50">
                    <!-- Qualification Type Info -->
                    <td>
                        {{ qualificationType.qualificationTypeName }}
                    </td>

                    <!-- Description -->
                    <td>
                        <div class="max-w-md">
                            <p class="text-sm text-gray-700 line-clamp-2">
                                {{ qualificationType.qualificationTypeDescription }}
                            </p>
                        </div>
                    </td>

                    <!-- Status -->
                    <td class="text-center">
                        <p-tag [value]="getStatusText(qualificationType.isActive)"
                            [severity]="getStatusSeverity(qualificationType.isActive)"
                            [icon]="getStatusIcon(qualificationType.isActive)">
                        </p-tag>
                    </td>

                    <!-- Actions -->
                    <td class="text-center">
                        <div class="flex items-center justify-center gap-2">
                            <!-- Edit Button -->
                            <p-button icon="pi pi-pencil" [rounded]="true" [outlined]="true"
                                pTooltip="Edit qualification type" tooltipPosition="top"
                                (onClick)="onEdit(qualificationType)" [disabled]="isPerformingAction">
                            </p-button>

                            <!-- Delete Button -->
                            <p-button icon="pi pi-trash" [rounded]="true" [outlined]="true" severity="danger"
                                pTooltip="Delete qualification type" tooltipPosition="top"
                                (onClick)="onDelete(qualificationType.qualificationTypeId)"
                                [disabled]="isPerformingAction">
                            </p-button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <!-- Empty Message -->
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="4" class="text-center py-8">
                        <div class="flex flex-col items-center gap-3">
                            <i class="pi pi-search text-3xl text-gray-400"></i>
                            <p class="text-gray-600">No qualification types found matching your criteria</p>
                        </div>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>