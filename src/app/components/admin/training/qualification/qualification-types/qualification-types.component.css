/* Custom styles for qualification types management */

.custom-spinner {
  color: #198BDB;
}

.p-datatable .p-datatable-thead>tr>th {
  background-color: #f8fafc;
  border-color: #e2e8f0;
  color: #374151;
  font-weight: 600;
}

.p-datatable .p-datatable-tbody>tr {
  border-color: #e5e7eb;
}

.p-datatable .p-datatable-tbody>tr:hover {
  background-color: #f9fafb;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.p-tag.p-tag-success {
  background-color: #dcfce7;
  color: #166534;
}

.p-tag.p-tag-danger {
  background-color: #fecaca;
  color: #991b1b;
}

.p-button.p-button-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.p-progressspinner .p-progressspinner-circle {
  stroke: #198BDB;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .p-datatable .p-datatable-thead>tr>th:nth-child(2) {
    display: none;
  }

  .p-datatable .p-datatable-tbody>tr>td:nth-child(2) {
    display: none;
  }
}