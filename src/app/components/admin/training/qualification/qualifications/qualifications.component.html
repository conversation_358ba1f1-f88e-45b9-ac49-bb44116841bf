<p-toast></p-toast>
<p-confirmDialog></p-confirmDialog>

<!-- Header Section -->
<div class="w-full">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364]">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-2xl sm:text-3xl">Qualifications Management</span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <!-- Qualifications Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Table Header Actions -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-graduation-cap text-blue-600"></i>
                        Qualifications List
                    </h3>
                    <p class="text-sm text-gray-600">Manage qualifications offered by various professional bodies</p>
                </div>
                
                <!-- Search and Actions -->
                <div class="flex flex-col sm:flex-row gap-3 sm:items-center">
                    <!-- Create Button -->
                    <p-button 
                        icon="pi pi-plus" 
                        label="Create Qualification"
                        styleClass="p-button-primary"
                        (onClick)="onCreateNew()">
                    </p-button>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <p-table 
            [value]="getFilteredQualifications()" 
            [paginator]="true" 
            [rows]="10" 
            [rowsPerPageOptions]="[10, 25, 50]"
            [globalFilterFields]="['qualificationName', 'professionalBodyName', 'qualificationTypeName']"
            dataKey="qualificationId"
            responsiveLayout="scroll"
            [scrollable]="true"
            scrollHeight="500px"
            styleClass="p-datatable-sm">

            <!-- Table Header -->
            <ng-template pTemplate="header">
                <tr>
                    <th scope="col" style="width: 35%">
                        <div class="flex items-center gap-2">
                            Qualification Name
                        </div>
                    </th>
                    <th scope="col" style="width: 25%">Professional Body</th>
                    <th scope="col" style="width: 25%">Qualification Type</th>
                    <th scope="col" style="width: 15%" class="text-center">Actions</th>
                </tr>
            </ng-template>

            <!-- Table Body -->
            <ng-template pTemplate="body" let-qualification>
                <tr class="hover:bg-gray-50 transition-colors">
                    <!-- Qualification Name -->
                    <td>
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="pi pi-graduation-cap text-blue-600 text-xs"></i>
                            </div>
                            <div class="min-w-0 flex-1">
                                <p class="font-medium text-gray-900 truncate">
                                    {{ qualification.qualificationName }}
                                </p>
                            </div>
                        </div>
                    </td>

                    <!-- Professional Body -->
                    <td>
                        <div class="flex items-center gap-2">
                            <i class="pi pi-building text-gray-400"></i>
                            <span class="text-gray-900">{{ qualification.professionalBodyName }}</span>
                        </div>
                    </td>

                    <!-- Qualification Type -->
                    <td>
                        <p-tag 
                            [value]="qualification.qualificationTypeName"
                            severity="info"
                            styleClass="text-xs">
                        </p-tag>
                    </td>

                    <!-- Actions -->
                    <td class="text-center">
                        <div class="flex items-center justify-center gap-1">
                            <!-- Edit Button -->
                            <p-button 
                                icon="pi pi-pencil" 
                                styleClass="p-button-text p-button-sm p-button-rounded"
                                pTooltip="Edit qualification"
                                tooltipPosition="top"
                                (onClick)="onEdit(qualification)">
                            </p-button>

                            <!-- Delete Button -->
                            <p-button 
                                icon="pi pi-trash" 
                                styleClass="p-button-text p-button-sm p-button-rounded p-button-danger"
                                pTooltip="Delete qualification"
                                tooltipPosition="top"
                                (onClick)="onDelete(qualification)">
                            </p-button>
                        </div>
                    </td>
                </tr>
            </ng-template>

            <!-- Empty State -->
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="4" class="text-center py-12">
                        <div class="flex flex-col items-center gap-4">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="pi pi-graduation-cap text-gray-400 text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-1">No Qualifications Found</h3>
                                <p class="text-gray-500 mb-4">
                                    {{ globalFilter || selectedProfessionalBody || selectedQualificationType 
                                        ? 'No qualifications match your search criteria.' 
                                        : 'Get started by creating your first qualification.' }}
                                </p>
                                <p-button 
                                    *ngIf="!globalFilter && !selectedProfessionalBody && !selectedQualificationType"
                                    icon="pi pi-plus" 
                                    label="Create Qualification"
                                    styleClass="p-button-primary"
                                    (onClick)="onCreateNew()">
                                </p-button>
                                <p-button 
                                    *ngIf="globalFilter || selectedProfessionalBody || selectedQualificationType"
                                    icon="pi pi-filter-slash" 
                                    label="Clear Filters"
                                    styleClass="p-button-outlined"
                                    (onClick)="clearFilters()">
                                </p-button>
                            </div>
                        </div>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>
