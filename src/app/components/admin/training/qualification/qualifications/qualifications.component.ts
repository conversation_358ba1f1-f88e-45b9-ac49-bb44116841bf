import {
  Component,
  OnInit,
  On<PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { TagModule } from 'primeng/tag';
import { MenuItem, MessageService, ConfirmationService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { QualificationService } from '../../../../../services/admin/training/qualification.service';
import { Qualification } from '../../../../../models/admin/training/qualification.model';

@Component({
  selector: 'app-qualifications',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    ToastModule,
    ConfirmDialogModule,
    InputTextModule,
    DropdownModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    OverlayPanelModule,
    TagModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './qualifications.component.html',
  styleUrl: './qualifications.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QualificationsComponent implements OnInit, OnDestroy {
  private readonly qualificationService = inject(QualificationService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();

  // Component state
  qualifications: Qualification[] = [];
  globalFilter = '';

  // Filter options
  professionalBodyOptions: any[] = [];
  qualificationTypeOptions: any[] = [];
  selectedProfessionalBody: string | null = null;
  selectedQualificationType: string | null = null;

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.loadQualifications();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/dashboard' };
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      { label: 'Qualifications' },
    ];
  }

  loadQualifications(): void {
    this.cdr.markForCheck();

    this.qualificationService
      .getAllQualifications()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data: Qualification[]) => {
          this.qualifications = data;
          this.buildFilterOptions();
          this.cdr.markForCheck();
        },
        error: (error: any) => {
          console.error('Error loading qualifications:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load qualifications',
          });
        },
      });
  }

  private buildFilterOptions(): void {
    // Build unique professional body options
    const professionalBodies = [...new Set(this.qualifications.map(q => q.professionalBodyName))];
    this.professionalBodyOptions = professionalBodies.map(name => ({
      label: name,
      value: name
    }));

    // Build unique qualification type options
    const qualificationTypes = [...new Set(this.qualifications.map(q => q.qualificationTypeName))];
    this.qualificationTypeOptions = qualificationTypes.map(name => ({
      label: name,
      value: name
    }));
  }

  onCreateNew(): void {
    this.router.navigate(['/admin/training/create-qualification']);
  }

  onEdit(qualification: Qualification): void {
    this.router.navigate(['/admin/training/create-qualification'], {
      queryParams: { edit: true, id: qualification.qualificationId },
    });
  }

  onDelete(qualification: Qualification): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete "${qualification.qualificationName}"? This action cannot be undone.`,
      header: 'Confirm Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-secondary',
      accept: () => {
        this.deleteQualification(qualification.qualificationId);
      },
    });
  }

  private deleteQualification(id: string): void {
    this.qualificationService
      .deleteQualification(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Qualification deleted successfully',
          });
          this.loadQualifications();
        },
        error: (error: any) => {
          console.error('Error deleting qualification:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to delete qualification',
          });
        },
      });
  }

  onGlobalFilter(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.globalFilter = target.value;
  }

  onProfessionalBodyFilter(event: any): void {
    this.selectedProfessionalBody = event.value;
  }

  onQualificationTypeFilter(event: any): void {
    this.selectedQualificationType = event.value;
  }

  clearFilters(): void {
    this.globalFilter = '';
    this.selectedProfessionalBody = null;
    this.selectedQualificationType = null;
  }

  getFilteredQualifications(): Qualification[] {
    let filtered = [...this.qualifications];

    if (this.selectedProfessionalBody) {
      filtered = filtered.filter(q => q.professionalBodyName === this.selectedProfessionalBody);
    }

    if (this.selectedQualificationType) {
      filtered = filtered.filter(q => q.qualificationTypeName === this.selectedQualificationType);
    }

    return filtered;
  }

  getUniqueCountsByProperty(property: keyof Qualification): number {
    return new Set(this.qualifications.map(q => q[property])).size;
  }
}
