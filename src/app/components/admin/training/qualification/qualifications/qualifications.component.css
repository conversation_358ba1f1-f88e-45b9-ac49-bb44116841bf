/* Table styling enhancements */
::ng-deep .p-datatable .p-datatable-thead > tr > th {
  background-color: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
  background-color: #f9fafb;
}

/* Search input styling */
::ng-deep .p-inputtext:focus {
  border-color: #198BDB;
  box-shadow: 0 0 0 2px rgba(25, 139, 219, 0.2);
}

/* Dropdown styling */
::ng-deep .p-dropdown:not(.p-disabled):hover .p-dropdown-trigger,
::ng-deep .p-dropdown:not(.p-disabled):hover .p-dropdown-label {
  border-color: #198BDB;
}

/* Tag styling for qualification types */
::ng-deep .p-tag.p-tag-info {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Progress spinner styling */
::ng-deep .p-progress-spinner-circle {
  stroke: #198BDB;
}