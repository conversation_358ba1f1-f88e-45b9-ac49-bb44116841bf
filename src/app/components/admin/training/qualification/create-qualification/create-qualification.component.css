/* Custom spinner styling */
::ng-deep .custom-spinner .p-progress-spinner-circle {
  stroke: #198BDB;
}

/* Form validation styling */
::ng-deep .p-dropdown.ng-invalid:not(.ng-pristine) .p-dropdown-trigger,
::ng-deep .p-dropdown.ng-invalid:not(.ng-pristine) .p-dropdown-label {
  border-color: #dc2626;
}

::ng-deep .p-inputtext.ng-invalid:not(.ng-pristine) {
  border-color: #dc2626;
}

/* Focus states for form controls */
::ng-deep .p-dropdown:not(.p-disabled):hover .p-dropdown-trigger,
::ng-deep .p-dropdown:not(.p-disabled):hover .p-dropdown-label {
  border-color: #198BDB;
}

::ng-deep .p-inputtext:focus {
  border-color: #198BDB;
  box-shadow: 0 0 0 2px rgba(25, 139, 219, 0.2);
}