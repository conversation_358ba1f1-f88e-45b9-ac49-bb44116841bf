import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { MenuItem, MessageService, ConfirmationService } from 'primeng/api';
import { Subject, takeUntil, finalize, forkJoin } from 'rxjs';
import { QualificationService } from '../../../../../services/admin/training/qualification.service';
import { ProfessionalBodyService } from '../../../../../services/admin/training/professional-body.service';
import { QualificationTypeService } from '../../../../../services/admin/training/qualification-type.service';
import { Qualification, CreateQualification } from '../../../../../models/admin/training/qualification.model';
import { ProfessionalBody } from '../../../../../models/admin/training/professional-body.model';
import { QualificationType } from '../../../../../models/admin/training/qualification-type.model';

@Component({
  selector: 'app-create-qualification',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './create-qualification.component.html',
  styleUrl: './create-qualification.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateQualificationComponent implements OnInit, OnDestroy {
  private readonly fb = inject(FormBuilder);
  private readonly qualificationService = inject(QualificationService);
  private readonly professionalBodyService = inject(ProfessionalBodyService);
  private readonly qualificationTypeService = inject(QualificationTypeService);
  private readonly messageService = inject(MessageService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly destroy$ = new Subject<void>();

  // Component state
  isSubmitting = false;
  isUpdateMode = false;
  qualificationId: string | null = null;

  // Form configuration
  qualificationForm!: FormGroup;

  // Dropdown data
  professionalBodies: ProfessionalBody[] = [];
  qualificationTypes: QualificationType[] = [];

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.initializeForm();
    this.loadDropdownData();
    this.checkRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/dashboard' };
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      { label: 'Qualifications', routerLink: '/dashboard/admin/training/qualifications' },
      { label: this.isUpdateMode ? 'Edit Qualification' : 'Create Qualification' },
    ];
  }

  private initializeForm(): void {
    this.qualificationForm = this.fb.group({
      qualificationName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(200)]],
      professionalBodyId: ['', [Validators.required]],
      qualificationTypeId: ['', [Validators.required]]
    });
  }

  private loadDropdownData(): void {
    this.cdr.markForCheck();

    forkJoin({
      professionalBodies: this.professionalBodyService.getAllProfessionalBodiesToList(),
      qualificationTypes: this.qualificationTypeService.getAllQualificationTypesToList()
    })
    .pipe(
      takeUntil(this.destroy$),
      finalize(() => {
        this.cdr.markForCheck();
      })
    )
    .subscribe({
      next: (data) => {
        this.professionalBodies = data.professionalBodies;
        this.qualificationTypes = data.qualificationTypes;
        this.cdr.markForCheck();
      },
      error: (error) => {
        console.error('Error loading dropdown data:', error);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load dropdown data',
        });
      },
    });
  }

  private checkRouteParams(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['edit'] && params['id']) {
        this.isUpdateMode = true;
        this.qualificationId = params['id'];
        this.updateBreadcrumbForEdit();
        if (this.qualificationId) {
          this.loadQualificationData(this.qualificationId);
        }
      }
    });
  }

  private updateBreadcrumbForEdit(): void {
    this.breadcrumbItems = [
      { label: 'Administration', routerLink: '/dashboard/admin' },
      { label: 'Training Management', routerLink: '/dashboard/admin/training' },
      { label: 'Qualifications', routerLink: '/dashboard/admin/training/qualifications' },
      { label: 'Edit Qualification' },
    ];
  }

  private loadQualificationData(id: string): void {
    this.cdr.markForCheck();

    this.qualificationService
      .getQualificationById(id)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data: Qualification) => {
          this.populateForm(data);
        },
        error: (error: any) => {
          console.error('Error loading qualification data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load qualification data',
          });
          this.router.navigate(['/dashboard/admin/training/qualifications']);
        },
      });
  }

  private populateForm(data: Qualification): void {
    // Find the corresponding IDs for the form
    const professionalBody = this.professionalBodies.find(pb => pb.professionalBodyName === data.professionalBodyName);
    const qualificationType = this.qualificationTypes.find(qt => qt.qualificationTypeName === data.qualificationTypeName);

    this.qualificationForm.patchValue({
      qualificationName: data.qualificationName,
      professionalBodyId: professionalBody?.professionalBodyId || '',
      qualificationTypeId: qualificationType?.qualificationTypeId || '',
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.qualificationForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldErrorMessage(fieldName: string): string {
    const field = this.qualificationForm.get(fieldName);
    if (!field || !field.errors) return '';

    const errors = field.errors;

    if (errors['required']) {
      switch (fieldName) {
        case 'qualificationName':
          return 'Qualification name is required';
        case 'professionalBodyId':
          return 'Professional body is required';
        case 'qualificationTypeId':
          return 'Qualification type is required';
        default:
          return 'This field is required';
      }
    }

    if (errors['minlength']) {
      switch (fieldName) {
        case 'qualificationName':
          return 'Name must be at least 2 characters long';
        default:
          return `Minimum length is ${errors['minlength'].requiredLength} characters`;
      }
    }

    if (errors['maxlength']) {
      switch (fieldName) {
        case 'qualificationName':
          return 'Name cannot exceed 200 characters';
        default:
          return `Maximum length is ${errors['maxlength'].requiredLength} characters`;
      }
    }

    return 'Invalid input';
  }

  onSubmit(): void {
    if (this.qualificationForm.valid) {
      this.isSubmitting = true;
      this.cdr.markForCheck();

      const formData = this.qualificationForm.value;

      const baseData: CreateQualification = {
        qualificationName: formData.qualificationName!,
        professionalBodyId: formData.professionalBodyId!,
        qualificationTypeId: formData.qualificationTypeId!,
      };

      const operation = this.isUpdateMode
        ? this.qualificationService.updateQualification({
            ...baseData,
            qualificationId: this.qualificationId!,
            // Include the display names for update
            professionalBodyName: this.professionalBodies.find(pb => pb.professionalBodyId === baseData.professionalBodyId)?.professionalBodyName || '',
            qualificationTypeName: this.qualificationTypes.find(qt => qt.qualificationTypeId === baseData.qualificationTypeId)?.qualificationTypeName || ''
          })
        : this.qualificationService.createQualification(baseData);

      operation
        .pipe(
          takeUntil(this.destroy$),
          finalize(() => {
            this.isSubmitting = false;
            this.cdr.markForCheck();
          })
        )
        .subscribe({
          next: (response) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: this.isUpdateMode
                ? 'Qualification updated successfully'
                : 'Qualification created successfully',
            });

            setTimeout(() => {
              this.router.navigate(['/admin/training/qualifications']);
            }, 500);
          },
          error: (error) => {
            console.error('Error submitting qualification:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: this.isUpdateMode
                ? 'Failed to update qualification'
                : 'Failed to create qualification',
            });
          },
        });
    } else {
      this.markFormGroupTouched(this.qualificationForm);
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly',
      });
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  onCancel(): void {
    if (this.qualificationForm.dirty) {
      this.confirmationService.confirm({
        message: 'You have unsaved changes. Are you sure you want to cancel?',
        header: 'Confirm Cancel',
        icon: 'pi pi-exclamation-triangle',
        acceptButtonStyleClass: 'p-button-danger',
        rejectButtonStyleClass: 'p-button-secondary',
        accept: () => {
          this.navigateBack();
        },
      });
    } else {
      this.navigateBack();
    }
  }

  private navigateBack(): void {
    this.router.navigate(['/admin/training/qualifications']);
  }

  onReset(): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to reset the form? All unsaved changes will be lost.',
      header: 'Confirm Reset',
      icon: 'pi pi-exclamation-triangle',
      acceptButtonStyleClass: 'p-button-warning',
      rejectButtonStyleClass: 'p-button-secondary',
      accept: () => {
        this.qualificationForm.reset();
        this.messageService.add({
          severity: 'info',
          summary: 'Form Reset',
          detail: 'Form has been reset to default values',
        });
      },
    });
  }
}
