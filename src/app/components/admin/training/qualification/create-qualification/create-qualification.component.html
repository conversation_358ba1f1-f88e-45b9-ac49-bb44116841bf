<p-toast></p-toast>
<p-confirmDialog></p-confirmDialog>

<!-- Header Section -->
<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-2xl sm:text-3xl">
                {{ isUpdateMode ? 'Edit Qualification' : 'Create Qualification' }}
            </span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <div class="max-w-3xl mx-auto">
        <form [formGroup]="qualificationForm" (ngSubmit)="onSubmit()">
            
            <!-- Basic Information Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-graduation-cap text-blue-600"></i>
                        Qualification Information
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">
                        {{ isUpdateMode ? 'Update the qualification details' : 'Provide information for the new qualification' }}
                    </p>
                </div>

                <div class="space-y-6">
                    <!-- Qualification Name -->
                    <div>
                        <label for="qualificationName" class="block text-sm font-medium text-gray-700 mb-2">
                            Qualification Name <span class="text-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            id="qualificationName" 
                            pInputText 
                            formControlName="qualificationName" 
                            class="w-full h-11"
                            placeholder="Enter qualification name (e.g., ACCA Professional Level)"
                            [class.ng-invalid]="isFieldInvalid('qualificationName')"
                            maxlength="200">
                        <small *ngIf="isFieldInvalid('qualificationName')" 
                               class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('qualificationName') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            {{ qualificationForm.get('qualificationName')?.value?.length || 0 }}/200 characters
                        </small>
                    </div>

                    <!-- Professional Body -->
                    <div>
                        <label for="professionalBodyId" class="block text-sm font-medium text-gray-700 mb-2">
                            Professional Body <span class="text-red-500">*</span>
                        </label>
                        <p-dropdown 
                            id="professionalBodyId"
                            formControlName="professionalBodyId"
                            [options]="professionalBodies"
                            optionLabel="professionalBodyName"
                            optionValue="professionalBodyId"
                            placeholder="Select professional body"
                            [filter]="true"
                            filterBy="professionalBodyName"
                            [showClear]="true"
                            class="w-full"
                            [class.ng-invalid]="isFieldInvalid('professionalBodyId')">
                        </p-dropdown>
                        <small *ngIf="isFieldInvalid('professionalBodyId')" 
                               class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('professionalBodyId') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            Select the professional body that offers this qualification
                        </small>
                    </div>

                    <!-- Qualification Type -->
                    <div>
                        <label for="qualificationTypeId" class="block text-sm font-medium text-gray-700 mb-2">
                            Qualification Type <span class="text-red-500">*</span>
                        </label>
                        <p-dropdown 
                            id="qualificationTypeId"
                            formControlName="qualificationTypeId"
                            [options]="qualificationTypes"
                            optionLabel="qualificationTypeName"
                            optionValue="qualificationTypeId"
                            placeholder="Select qualification type"
                            [filter]="true"
                            filterBy="qualificationTypeName"
                            [showClear]="true"
                            class="w-full"
                            [class.ng-invalid]="isFieldInvalid('qualificationTypeId')">
                        </p-dropdown>
                        <small *ngIf="isFieldInvalid('qualificationTypeId')" 
                               class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('qualificationTypeId') }}
                        </small>
                        <small class="text-gray-500 mt-1 block">
                            Choose the type/category of this qualification
                        </small>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                    <!-- Cancel Button -->
                    <p-button 
                        type="button" 
                        icon="pi pi-times" 
                        label="Cancel"
                        styleClass="p-button-outlined w-full sm:w-auto" 
                        (onClick)="onCancel()"
                        [disabled]="isSubmitting">
                    </p-button>

                    <!-- Submit Button -->
                    <p-button 
                        type="submit" 
                        icon="pi pi-check"
                        [label]="isUpdateMode ? 'Update Qualification' : 'Create Qualification'"
                        [disabled]="qualificationForm.invalid || isSubmitting" 
                        [loading]="isSubmitting"
                        styleClass="p-button-primary w-full sm:w-auto" 
                        [style]="{ 'min-width': '200px' }">
                    </p-button>
                </div>

                <!-- Form Status Message -->
                <div *ngIf="qualificationForm.invalid && qualificationForm.touched"
                     class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center gap-2">
                        <i class="pi pi-exclamation-triangle text-red-600"></i>
                        <p class="text-sm text-red-800">
                            Please fill in all required fields correctly before submitting.
                        </p>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
