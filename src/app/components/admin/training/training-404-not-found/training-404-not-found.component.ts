import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-training-404-not-found',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule
  ],
  templateUrl: './training-404-not-found.component.html',
  styleUrl: './training-404-not-found.component.css'
})
export class Training404NotFoundComponent implements OnInit {

  constructor(private readonly router: Router) {}

  ngOnInit(): void {
    // Optional: Track 404 errors for analytics
    console.warn('Training 404 page accessed:', window.location.pathname);
  }

  /**
   * Navigate back to the main training dashboard
   */
  goToTrainingDashboard(): void {
    this.router.navigate(['/admin/training']);
  }

  /**
   * Navigate to training organization management
   */
  goToOrganizations(): void {
    this.router.navigate(['/admin/training/organizations']);
  }

  /**
   * Navigate to training approvals
   */
  goToApprovals(): void {
    this.router.navigate(['/admin/training/approvals']);
  }

  /**
   * Navigate to application home page
   */
  goHome(): void {
    this.router.navigate(['/']);
  }

  /**
   * Go back to the previous page using browser history
   */
  goBack(): void {
    window.history.back();
  }
  /**
   * Navigate to help or documentation section
   */
  goToHelp(): void {
    this.router.navigate(['/help']);
  }

  /**
   * Refresh the current page
   */
  refreshPage(): void {
    window.location.reload();
  }
}
