.not-found-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #E0EAFC 0%, #CFDEF3 100%);
    text-align: center;
}

h1 {
    font-size: 3.5em;
    color: #343a40;
    margin-bottom: 0.3em;
}

p {
    font-size: 1.3em;
    color: #495057;
    margin-bottom: 2.5em;
    padding: 0 20px;
    line-height: 1.6;
}

.home-link {
    padding: 1.2em 2.5em;
    background-color: #3880ff;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    font-weight: 500;
    letter-spacing: 0.5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.home-link:hover {
    background-color: #2a6496;
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.2);
}
