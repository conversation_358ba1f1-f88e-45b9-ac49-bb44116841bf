/* Custom styles for the About page */

/* Animation for cards on hover */
.bg-gray-50:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Better typography for content */
p.text-gray-700 {
  line-height: 1.7;
}

/* Enhanced image styling */
.rounded-full img {
  transition: transform 0.5s ease;
}

.rounded-full:hover img {
  transform: scale(1.05);
}

/* Social media icons hover effects */
.flex.space-x-4 a {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.flex.space-x-4 a:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Stats section hover effects */
.bg-white\/20 {
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.bg-white\/20:hover {
  transform: translateY(-3px);
  background-color: rgba(255, 255, 255, 0.25);
}