import { Component, inject, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { DrawerModule } from 'primeng/drawer';
import { AuthService } from '../../../../services/auth/auth.service';
import { User, UserRole } from '../../../../models/auth/auth';
import { CommonModule } from '@angular/common';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [DrawerModule, RouterModule, CommonModule, ToastModule],
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
  providers: [MessageService],
})
export class NavbarComponent implements OnInit {
  private readonly router: Router = inject(Router);
  private readonly authService = inject(AuthService);
  private readonly messageService = inject(MessageService);
  displaySidebar: boolean = false;
  isAuthenticated: boolean = false;
  user: User | null = null;

  ngOnInit(): void {
    this.user = this.authService.getCurrentUser();
    this.isAuthenticated = this.authService.isAuthenticated();
  }

  goToHome() {
    this.router.navigate(['']);
  }

  goToDashboard() {
    if (this.user?.roles.includes(UserRole.ADMIN)) {
      this.router.navigate(['/admin']);
    } else {
      this.router.navigate(['/dashboard']);
    }
  }

  goToJobBank() {
    this.messageService.add({
      severity: 'warn',
      summary: 'Authentication Required',
      detail: 'Please login to access the Job Bank',
      life: 3000,
    });
    this.router.navigate(['/aat-job-bank']);
  }

  goToHelp() {
    this.router.navigate(['/help']);
  }

  toggleSidebar() {
    this.displaySidebar = !this.displaySidebar;
  }
}
