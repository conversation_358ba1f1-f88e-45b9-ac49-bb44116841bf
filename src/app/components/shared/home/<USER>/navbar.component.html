<p-toast />

<div class="navbar-container pt-4 pb-4 bg-white shadow-md">
  <div class="w-4/5 mx-auto flex justify-between items-center">
    <div class="logo">
      <img src="assets/images/aat_logo.webp" alt="Logo" class="h-16 cursor-pointer" routerLink="/">
    </div>
    <!-- Desktop Nav Items -->
    <div class="nav-items hidden md:flex space-x-4">
      <a routerLink="" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" (click)="goToHome()"
        class="mt-2 nav-link text-gray-500 hover:text-gray-700 transition-colors duration-300 cursor-pointer">Home</a>
      <a routerLink="/help" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" (click)="goToHelp()"
        class="mt-2 nav-link text-gray-500 hover:text-gray-700 transition-colors duration-300 cursor-pointer">Help</a>
      <a *ngIf="isAuthenticated" routerLink="/dashboard" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" (click)="goToDashboard()"
        class="dashboard-btn transition-colors duration-300 cursor-pointer">Dashboard</a>

    </div>
    <!-- Mobile Hamburger Icon -->
    <button (click)="toggleSidebar()" class="block md:hidden focus:outline-none">
      <!-- Hamburger SVG Icon -->
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
      </svg>
    </button>
  </div>
</div>

<!-- Mobile Drawer using PrimeNG -->
<p-drawer [(visible)]="displaySidebar" position="left" [baseZIndex]="10000" (onHide)="displaySidebar=false">
  <div class="flex flex-col space-y-4 p-4">
    <a routerLink="" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" (click)="goToHome(); displaySidebar=false" class="nav-link">Home</a>
    <a routerLink="/aat-job-bank" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" (click)="goToJobBank(); displaySidebar=false" class="nav-link">AAT Job Bank</a>
    <a routerLink="/dashboard" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" (click)="goToDashboard(); displaySidebar=false" class="dashboard-btn">Dashboard</a>
  </div>
</p-drawer>