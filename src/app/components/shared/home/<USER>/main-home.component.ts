import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DatePickerModule } from 'primeng/datepicker';
import { FormsModule } from '@angular/forms';
import { NavbarComponent } from "../navbar/navbar.component";

import { TabsModule } from 'primeng/tabs';
import { FooterComponent } from "../footer/footer.component";
import { SelectModule } from 'primeng/select';
import { AuthComponent } from "../../../auth/auth.component";
import { AffiliatedTrainingPartnersComponent } from '../affiliated-training-partners/affiliated-training-partners.component';
import { JobFairHighlightsComponent } from '../job-fair-highlights/job-fair-highlights.component';
import { AccordionModule } from 'primeng/accordion';
import { AuthService } from '../../../../services/auth/auth.service';

@Component({
  selector: 'app-main-home',
  templateUrl: './main-home.component.html',
  styleUrls: ['./main-home.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    CheckboxModule,
    DatePickerModule,
    FormsModule,
    NavbarComponent,
    TabsModule,
    SelectModule,
    AuthComponent,
    AccordionModule,
    AffiliatedTrainingPartnersComponent,
    JobFairHighlightsComponent,
    FooterComponent
]
})
export class MainHomeComponent implements OnInit{
  private readonly authService = inject(AuthService);
  date: Date = new Date();
  isLoggedIn: boolean = false;

  ngOnInit(): void {
    this.isLoggedIn = this.authService.isAuthenticated();
  }
}
