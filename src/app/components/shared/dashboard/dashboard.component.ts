import { Component, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { RouterOutlet, Router } from '@angular/router';
import { AuthService } from '../../../services/auth/auth.service';
import { MenuItem } from 'primeng/api';
import { CommonModule } from '@angular/common';
import { interval, Subscription } from 'rxjs';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [SidebarComponent, RouterOutlet, CommonModule],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.css',
})
export class DashboardComponent implements OnInit, OnDestroy {
  isSidebarCollapsed = false;
  private readonly _authService = inject(AuthService);
  private readonly router = inject(Router);

  // User information
  public userName: string = '';

  // Date and time
  public currentTime: Date = new Date();
  public greeting: string = '';
  private clockSubscription: Subscription | null = null;

  // Menu toggles
  public isProfileMenuOpen: boolean = false;
  public isNotificationMenuOpen: boolean = false;
  public hasNotifications: boolean = true;

  items: MenuItem[] = [];
  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };
  componentTitle: string = '';

  ngOnInit(): void {
    const user = this._authService.getCurrentUser();
    this.userName = user?.username
      ? user.username.charAt(0).toUpperCase() + user.username.slice(1)
      : 'User';

    this.updateGreeting();
    this.clockSubscription = interval(1000).subscribe(() => {
      this.currentTime = new Date();
      this.updateGreeting();
    });

    document.addEventListener(
      'click',
      this.closeMenusOnClickOutside.bind(this)
    );
  }

  ngOnDestroy(): void {
    if (this.clockSubscription) {
      this.clockSubscription.unsubscribe();
    }
    document.removeEventListener(
      'click',
      this.closeMenusOnClickOutside.bind(this)
    );
  }

  onSidebarToggled(expanded: boolean) {
    this.isSidebarCollapsed = !expanded;
  }

  toggleNotificationMenu(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    this.isNotificationMenuOpen = !this.isNotificationMenuOpen;
    if (this.isNotificationMenuOpen) {
      this.isProfileMenuOpen = false;
    }
  }

  closeMenusOnClickOutside(event: MouseEvent): void {
    const profileSection = document.querySelector('.profile-section');
    const notificationSection = document.querySelector('.notification-section');

    if (profileSection && !profileSection.contains(event.target as Node)) {
      this.isProfileMenuOpen = false;
    }

    if (
      notificationSection &&
      !notificationSection.contains(event.target as Node)
    ) {
      this.isNotificationMenuOpen = false;
    }
  }

  updateGreeting(): void {
    const hour = this.currentTime.getHours();
    if (hour < 12) {
      this.greeting = 'Good Morning';
    } else if (hour < 18) {
      this.greeting = 'Good Afternoon';
    } else {
      this.greeting = 'Good Evening';
    }
  }
}
