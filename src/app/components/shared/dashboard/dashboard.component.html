<app-sidebar (sidebarToggled)="onSidebarToggled($event)"></app-sidebar>
<div class="main-content bg-gray-100 min-h-screen" [class.full-width]="isSidebarCollapsed">
    <div class="header w-full" [ngStyle]="
        isProfileMenuOpen || isNotificationMenuOpen
          ? { 'z-index': '1000' }
          : { 'z-index': '1' }">

        <!-- Single row with two columns -->
        <div class="flex justify-between items-center w-full">
            <!-- Left column: Greeting section -->
            <div class="greeting-section flex-1">
                <h2 class="greeting text-base sm:text-xl">{{ greeting }}, {{ userName }}</h2>
                <div class="datetime text-xs sm:text-sm">
                    {{ currentTime | date : "EEEE, MMMM d, y" }} |
                    {{ currentTime | date : "h:mm:ss a" }}
                </div>
            </div>

            <!-- Right column: Notification section -->
            <div class="flex items-center">
                <div class="relative notification-section">
                    <!-- Notification Icon -->
                    <i class="pi pi-bell text-xl cursor-pointer" (click)="toggleNotificationMenu()"></i>

                    <!-- Notification Dropdown -->
                    <div *ngIf="isNotificationMenuOpen"
                        class="absolute right-0 mt-2 w-80 bg-white border rounded-lg shadow-lg notification-dropdown">
                        <div class="p-3 border-b flex justify-between items-center">
                            <h3 class="font-semibold text-gray-700">Notifications</h3>
                            <span class="text-xs bg-primary-600 text-white px-2 py-1 rounded-full">2 New</span>
                        </div>
                        <div class="p-2 border-t bg-gray-50">
                            <button
                                class="w-full text-sm hover:text-primary-green p-2 rounded transition-colors duration-150">
                                View All Notifications
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <router-outlet></router-outlet>
</div>