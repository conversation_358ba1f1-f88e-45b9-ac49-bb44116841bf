import {
  Component,
  OnInit,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  TrackByFunction,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  FormArray,
  Validators,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { FileUploadModule } from 'primeng/fileupload';
import { ToastModule } from 'primeng/toast';
import { CardModule } from 'primeng/card';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MenuItem, MessageService } from 'primeng/api';
import {
  Subject,
  takeUntil,
  finalize,
  debounceTime,
  distinctUntilChanged,
} from 'rxjs';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { SupervisorService } from '../../../services/shared/supervisor.service';
import { Qualification } from '../../../models/shared/supervisor/qualification';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-supervisor',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputTextModule,
    CheckboxModule,
    ButtonModule,
    FileUploadModule,
    ToastModule,
    CardModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
  ],
  providers: [MessageService],
  templateUrl: './supervisor.component.html',
  styleUrl: './supervisor.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SupervisorComponent implements OnInit, OnDestroy {  private readonly fb = inject(FormBuilder);
  private readonly messageService = inject(MessageService);
  private readonly supervisorService = inject(SupervisorService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly sanitizer = inject(DomSanitizer);
  private readonly destroy$ = new Subject<void>();
  supervisorForm!: FormGroup;
  qualifications: Qualification[] = [];
  otherCertsUploaded = false;
  isUpdateMode = false;
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
    // Multiple file upload properties
  uploadedFiles: File[] = [];
  filePreviewUrls: SafeUrl[] = [];
  maxFileSize = 10485760; // 10MB
  allowedFileTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
  maxFiles = 5;

  // TrackBy function for performance optimization
  trackByQualificationId: TrackByFunction<Qualification> = (
    index: number,
    item: Qualification
  ) => item.qualificationId;

  // TrackBy function for uploaded files
  trackByFileName: TrackByFunction<File> = (index: number, item: File) => 
    item.name + item.size + item.lastModified;

  get membershipsFormArray(): FormArray {
    return this.supervisorForm.get('memberships') as FormArray;
  }

  ngOnInit(): void {
    this.initForm();
    this.initializeBreadcrumb();
    this.loadQualificationsAndInitializeMemberships();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.supervisorForm = this.fb.group({
      name: ['', [Validators.required]],
      location: ['', [Validators.required]],
      designation: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      contactNumber: [
        '',
        [Validators.required, Validators.pattern(/^\d{10}$/)],
      ],
      memberships: this.fb.array([]),
      otherCertifications: [null],
    });
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/' };
    this.breadcrumbItems = [
      { label: 'Supervisor Registration' }
    ];
  }

  private loadQualificationsAndInitializeMemberships(): void {
    this.cdr.markForCheck();

    this.supervisorService
      .getMemberships()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data) => {
          this.qualifications = data;
          this.batchCreateMembershipControls();
          this.setupOptimizedFormListeners();
          this.cdr.markForCheck();
        },
        error: (error) => {
          console.error('Error loading qualifications:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load membership options',
          });
        },
      });
  }

  private batchCreateMembershipControls(): void {
    // Batch create all controls at once for better performance
    const membershipControls = this.qualifications.map(() =>
      this.fb.control(false)
    );

    // Replace the entire FormArray instead of adding controls one by one
    const membershipsFormArray = this.fb.array(membershipControls);
    this.supervisorForm.setControl('memberships', membershipsFormArray);
  }
  private setupOptimizedFormListeners(): void {
    // Use debouncing to prevent excessive processing
    this.membershipsFormArray.valueChanges
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(100),
        distinctUntilChanged()
      )
      .subscribe((memberships: boolean[]) => {
        const hasMembership =
          memberships?.some((checked: boolean) => checked) ?? false;

        if (!hasMembership) {
          this.supervisorForm.get('caCertificate')?.setValue(null);
        }

        this.cdr.markForCheck();
      });
  }

  // Efficient methods for membership management
  onMembershipChange(index: number, isChecked: boolean): void {
    const membershipControl = this.membershipsFormArray.at(index);
    membershipControl?.setValue(isChecked);
  }

  isMembershipSelected(index: number): boolean {
    return this.membershipsFormArray.at(index)?.value ?? false;
  }

  // Utility method to get selected membership IDs
  getSelectedMembershipIds(): string[] {
    return this.qualifications
      .filter((_, index) => this.isMembershipSelected(index))
      .map((qualification) => qualification.qualificationId);
  }  onOtherCertsUpload(event: any): void {
    const files = event.files ?? [];
    
    for (const file of files) {
      // Validate file type
      if (!this.allowedFileTypes.includes(file.type)) {
        this.messageService.add({
          severity: 'error',
          summary: 'Invalid File Type',
          detail: `File "${file.name}" has invalid type. Only PDF, JPEG, PNG, JPG files are allowed.`,
        });
        continue;
      }

      // Validate file size
      if (file.size > this.maxFileSize) {
        this.messageService.add({
          severity: 'error',
          summary: 'File Too Large',
          detail: `File "${file.name}" exceeds 10MB limit.`,
        });
        continue;
      }

      // Check if we haven't exceeded max files
      if (this.uploadedFiles.length >= this.maxFiles) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Maximum Files Reached',
          detail: `Cannot upload more than ${this.maxFiles} files.`,
        });
        break;
      }

      // Check for duplicate files
      const isDuplicate = this.uploadedFiles.some(
        (existingFile) => 
          existingFile.name === file.name && 
          existingFile.size === file.size &&
          existingFile.lastModified === file.lastModified
      );

      if (isDuplicate) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Duplicate File',
          detail: `File "${file.name}" has already been selected.`,
        });
        continue;
      }

      // Add file to uploaded files array
      this.uploadedFiles.push(file);

      // Create preview URL for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            const safeUrl = this.sanitizer.bypassSecurityTrustUrl(
              e.target.result as string
            );
            this.filePreviewUrls.push(safeUrl);
            this.cdr.markForCheck();
          }
        };
        reader.readAsDataURL(file);
      } else {
        // For non-image files, add null placeholder
        this.filePreviewUrls.push(null as any);
      }
    }

    // Update form control with uploaded files
    this.supervisorForm.get('otherCertifications')?.setValue(this.uploadedFiles);
    
    if (this.uploadedFiles.length > 0) {
      this.otherCertsUploaded = true;
      this.messageService.add({
        severity: 'success',
        summary: 'Files Uploaded',
        detail: `${this.uploadedFiles.length} file(s) uploaded successfully`,
      });
    }

    this.cdr.markForCheck();
  }

  removeFile(index: number): void {
    if (index >= 0 && index < this.uploadedFiles.length) {
      const removedFile = this.uploadedFiles[index];
      
      // Remove file and preview URL
      this.uploadedFiles.splice(index, 1);
      this.filePreviewUrls.splice(index, 1);
      
      // Update form control
      this.supervisorForm.get('otherCertifications')?.setValue(this.uploadedFiles);
      
      // Update upload status
      this.otherCertsUploaded = this.uploadedFiles.length > 0;
      
      this.messageService.add({
        severity: 'info',
        summary: 'File Removed',
        detail: `"${removedFile.name}" has been removed`,
      });
      
      this.cdr.markForCheck();
    }
  }

  getFileDisplayName(file: File): string {
    const maxLength = 30;
    if (file.name.length <= maxLength) {
      return file.name;
    }
    
    const extension = file.name.split('.').pop();
    const nameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.'));
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension!.length - 4);
    
    return `${truncatedName}...${extension}`;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private convertFilesToBase64(
    files: File[],
    callback: (filesData: Array<{base64: string, fileName: string, fileType: string}>) => void
  ): void {
    const filesData: Array<{base64: string, fileName: string, fileType: string}> = [];
    let processedCount = 0;

    if (files.length === 0) {
      callback(filesData);
      return;
    }

    files.forEach((file, index) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        const base64Content = base64String.split(',')[1];
        
        filesData[index] = {
          base64: base64Content,
          fileName: file.name,
          fileType: file.type
        };
        
        processedCount++;
        if (processedCount === files.length) {
          callback(filesData);
        }
      };
      reader.readAsDataURL(file);
    });
  }
  onSubmit(): void {
    if (this.supervisorForm.valid) {
      // Get selected membership IDs for submission
      const selectedMemberships = this.getSelectedMembershipIds();
      
      // Check if there are files to upload
      if (this.uploadedFiles.length > 0) {
        this.convertFilesToBase64(this.uploadedFiles, (filesData) => {
          const formData = {
            ...this.supervisorForm.value,
            selectedMemberships,
            otherCertifications: filesData,
          };
          
          this.submitSupervisorData(formData);
        });
      } else {
        const formData = {
          ...this.supervisorForm.value,
          selectedMemberships,
          otherCertifications: [],
        };
        
        this.submitSupervisorData(formData);
      }
    } else {
      this.validateAllFormFields();
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill all required fields',
      });
    }
  }
  private submitSupervisorData(formData: any): void {
    // Call the supervisor service to submit the data
    this.supervisorService
      .uploadSupervisorDocuments(formData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 200 || response.status === 201) {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Supervisor registration submitted successfully',
            });
            this.resetForm();
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to submit supervisor registration',
            });
          }
        },
        error: (error) => {
          console.error('Error submitting supervisor data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to submit supervisor registration. Please try again.',
          });
        },
      });
  }
  private resetForm(): void {
    this.supervisorForm.reset();
    this.otherCertsUploaded = false;
    this.uploadedFiles = [];
    this.filePreviewUrls = [];

    // Reinitialize membership controls
    this.batchCreateMembershipControls();
    this.cdr.markForCheck();
  }

  private validateAllFormFields(): void {
    Object.keys(this.supervisorForm.controls).forEach((field) => {
      const control = this.supervisorForm.get(field);
      control?.markAsTouched({ onlySelf: true });
    });    // Mark membership controls as touched
    this.membershipsFormArray.controls.forEach((control: any) => {
      control.markAsTouched();
    });
  }

  isFieldInvalid(field: string): boolean {
    const control = this.supervisorForm.get(field);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }
}
