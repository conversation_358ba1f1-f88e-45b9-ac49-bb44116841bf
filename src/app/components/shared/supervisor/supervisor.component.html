<p-toast></p-toast>

<div class="w-full px-4 py-4">
  <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
    <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
      <span class="text-white font-semibold text-3xl">
        {{ isUpdateMode ? 'Update Supervisor Details' : 'Supervisor Registration' }}
      </span>
      <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
    </div>
  </div>
</div>

<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
  <form [formGroup]="supervisorForm" (ngSubmit)="onSubmit()">

    <!-- Form Fields in Single Column -->
    <div class="flex flex-col gap-4 max-w-3xl mx-auto">
      <div class="mb-4">
        <label for="name" class="block font-semibold mb-2">Name <span class="text-red-500">*</span></label>
        <input type="text" pInputText id="name" formControlName="name" class="w-full"
          placeholder="Enter supervisor name">
        <small *ngIf="isFieldInvalid('name')" class="text-red-500 mt-1 block">Name is required</small>
      </div>

      <div class="mb-4">
        <label for="designation" class="block font-semibold mb-2">Designation <span
            class="text-red-500">*</span></label>
        <input type="text" pInputText id="designation" formControlName="designation" class="w-full"
          placeholder="Enter designation">
        <small *ngIf="isFieldInvalid('designation')" class="text-red-500 mt-1 block">Designation is required</small>
      </div>

      <div class="mb-4">
        <label for="contactNumber" class="block font-semibold mb-2">Contact number <span
            class="text-red-500">*</span></label>
        <input type="text" pInputText id="contactNumber" formControlName="contactNumber" class="w-full"
          placeholder="Enter contact number">
        <small *ngIf="isFieldInvalid('contactNumber')" class="text-red-500 mt-1 block">
          Valid contact number is required (10 digits)
        </small>
      </div>

      <div class="mb-4">
        <label for="location" class="block font-semibold mb-2">Location <span class="text-red-500">*</span></label>
        <input type="text" pInputText id="location" formControlName="location" class="w-full"
          placeholder="Enter location">
        <small *ngIf="isFieldInvalid('location')" class="text-red-500 mt-1 block">Location is required</small>
      </div>

      <div class="mb-4">
        <label for="email" class="block font-semibold mb-2">Email <span class="text-red-500">*</span></label>
        <input type="email" pInputText id="email" formControlName="email" class="w-full" placeholder="Enter email">
        <small *ngIf="isFieldInvalid('email')" class="text-red-500 mt-1 block">Valid email is required</small>
      </div>

      <!-- Memberships Section -->
      <div class="mb-4" formArrayName="memberships">
        <label class="block font-semibold mb-2">Memberships</label>
        <div class="flex flex-col gap-2">
          <div *ngFor="let qualification of qualifications; let i = index; trackBy: trackByQualificationId"
            class="flex items-center gap-2">
            <p-checkbox [value]="qualification.qualificationId" [binary]="true" [formControlName]="i"
              [inputId]="'membership-' + qualification.qualificationId">
            </p-checkbox>
            <label [for]="'membership-' + qualification.qualificationId" class="font-normal">
              {{ qualification.qualification }}
            </label>
          </div>
        </div>
      </div>
    </div>    <!-- File Upload Fields -->
    <div class="mt-6 flex flex-col gap-4 max-w-3xl mx-auto">
      <div class="w-full mb-4">
        <label for="otherCertifications" class="block font-semibold mb-2">Other Certifications</label>
        <p-fileUpload 
          id="otherCertifications" 
          mode="advanced" 
          [multiple]="true"
          [auto]="true" 
          chooseLabel="Upload Files"
          [showUploadButton]="false" 
          [showCancelButton]="false" 
          accept="application/pdf,image/*"
          [maxFileSize]="maxFileSize" 
          (onSelect)="onOtherCertsUpload($event)" 
          styleClass="w-full">
          <ng-template pTemplate="content">
            <div *ngIf="uploadedFiles.length > 0" class="mt-4">
              <h4 class="font-semibold mb-2">Uploaded Files ({{ uploadedFiles.length }}/{{ maxFiles }})</h4>
              <div class="grid grid-cols-1 gap-2">
                <div 
                  *ngFor="let file of uploadedFiles; let i = index; trackBy: trackByFileName" 
                  class="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-gray-50">
                  <div class="flex items-center gap-3">
                    <!-- File Type Icon -->
                    <div class="flex-shrink-0">
                      <i 
                        class="text-2xl"
                        [ngClass]="{
                          'pi pi-file-pdf text-red-500': file.type === 'application/pdf',
                          'pi pi-image text-blue-500': file.type.startsWith('image/'),
                          'pi pi-file text-gray-500': !file.type.startsWith('image/') && file.type !== 'application/pdf'
                        }">
                      </i>
                    </div>
                    
                    <!-- File Info -->
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 truncate" [title]="file.name">
                        {{ getFileDisplayName(file) }}
                      </p>
                      <p class="text-xs text-gray-500">
                        {{ formatFileSize(file.size) }}
                      </p>
                    </div>
                    
                    <!-- Image Preview (if applicable) -->
                    <div *ngIf="file.type.startsWith('image/') && filePreviewUrls[i]" class="flex-shrink-0">
                      <img 
                        [src]="filePreviewUrls[i]" 
                        alt="Preview" 
                        class="w-12 h-12 object-cover rounded border">
                    </div>
                  </div>
                  
                  <!-- Remove Button -->
                  <button 
                    type="button"
                    (click)="removeFile(i)"
                    class="flex-shrink-0 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                    title="Remove file">
                    <i class="pi pi-times text-sm"></i>
                  </button>
                </div>
              </div>
              
              <!-- Upload Summary -->
              <div class="mt-2 text-sm text-gray-600">
                <i class="pi pi-info-circle"></i>
                Maximum {{ maxFiles }} files allowed. Each file must be under {{ formatFileSize(maxFileSize) }}.
              </div>
            </div>
            
            <!-- Initial State Message -->
            <div *ngIf="uploadedFiles.length === 0" class="text-center text-gray-500 py-4">
              <i class="pi pi-cloud-upload text-3xl mb-2 block"></i>
              <p class="text-sm">Choose files or drag and drop here</p>
              <p class="text-xs">PDF, JPEG, PNG, JPG up to {{ formatFileSize(maxFileSize) }} each</p>
            </div>
          </ng-template>
        </p-fileUpload>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end gap-3 mt-10">
      <p-button type="submit" icon="pi pi-check" label="Submit" [disabled]="supervisorForm.invalid">
      </p-button>
    </div>
  </form>
</div>