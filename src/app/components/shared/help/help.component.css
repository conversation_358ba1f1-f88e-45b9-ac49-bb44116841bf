/* Custom styling for the FAQ page */
.faq-container {
  max-width: 900px;
  margin: 0 auto;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 100px;
}

.tab-button:hover {
  transform: translateY(-2px);
}

/* Accordion styling */
:host ::ng-deep .p-accordion .p-accordion-header .p-accordion-header-link {
  padding: 1rem 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;
  color: #111827;
  font-weight: 500;
  font-size: 1rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

:host ::ng-deep .p-accordion .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link {
  background-color: #f9fafb;
  border-color: #e5e7eb;
  color: #111827;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

:host ::ng-deep .p-accordion .p-accordion-header:not(.p-disabled):hover .p-accordion-header-link {
  background-color: #f3f4f6;
}

:host ::ng-deep .p-accordion .p-accordion-content {
  border: 1px solid #e5e7eb;
  border-top: none;
  border-radius: 0 0 0.5rem 0.5rem;
  padding: 1.25rem;
  background-color: #ffffff;
}

:host ::ng-deep .p-accordion .p-accordion-tab {
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
}

:host ::ng-deep .p-accordion .p-accordion-header-text {
  font-size: 1rem;
}

:host ::ng-deep .p-accordion .p-accordion-toggle-icon {
  font-size: 1rem;
}

:host ::ng-deep .p-button {
  transition: all 0.2s ease;
}

:host ::ng-deep .p-button-label {
  font-weight: 500;
}

/* Hide tab panel contents that aren't active */
.tab-content:not(.active) {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tab-button {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    min-width: 80px;
  }
  
  :host ::ng-deep .p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 0.875rem 1rem;
    font-size: 0.938rem;
  }
  
  :host ::ng-deep .p-accordion .p-accordion-content {
    padding: 1rem;
  }
  
  .faq-heading {
    font-size: 1.75rem;
  }
  
  .faq-subheading {
    font-size: 1rem;
  }
}