import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TabViewModule } from 'primeng/tabview';
import { NavbarComponent } from '../home/<USER>/navbar.component';
import { FooterComponent } from '../home/<USER>/footer.component';
import { CardModule } from 'primeng/card';
import { AccordionModule } from 'primeng/accordion';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { AvatarModule } from 'primeng/avatar';
import { BadgeModule } from 'primeng/badge';

@Component({
  selector: 'app-help',
  standalone: true,  imports: [
    CommonModule, 
    TabViewModule, 
    NavbarComponent, 
    FooterComponent, 
    CardModule, 
    AccordionModule,
    ButtonModule,
    DividerModule,
    AvatarModule,
    BadgeModule
  ],
  templateUrl: './help.component.html',
  styleUrl: './help.component.css'
})
export class HelpComponent {
  private readonly router = inject(Router);

  activeTab: string = 'Examination';
  
  setActiveTab(tabName: string): void {
    this.activeTab = tabName;
  }

  isHelpRoute(): boolean {
    return this.router.url === '/dashboard/help';
  }
}
