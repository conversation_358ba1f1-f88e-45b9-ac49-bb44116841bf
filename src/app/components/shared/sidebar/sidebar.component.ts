import { CommonModule } from '@angular/common';
import { Component, OnInit, Output, EventEmitter, Input, HostListener, inject } from '@angular/core';
import { RouterLink, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Router } from '@angular/router';
import { SidebarService as StudentSidebarService } from '../../../services/student/sidebar.service';
import { SidebarService as AdminSidebarService } from '../../../services/admin/admin-sidebar.service';
import { AuthService } from '../../../services/auth/auth.service';

interface MenuItem {
  label: string;
  icon: string;
  routerLink?: string;
  items?: MenuItem[];
  expanded?: boolean;
}

@Component({
  selector: 'app-sidebar',
  imports: [
    CommonModule,
    RouterModule,
    ButtonModule,
    InputTextModule,
    ReactiveFormsModule,
    RouterLink
  ],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css',
  animations: [
    trigger('slideInOut', [
      state('in', style({
        overflow: 'hidden',
        height: '*',
      })),
      state('out', style({
        opacity: '0',
        overflow: 'hidden',
        height: '0px',
      })),
      transition('in => out', animate('200ms ease-in-out')),
      transition('out => in', animate('200ms ease-in-out'))
    ]),
    trigger('sidebarAnimation', [
      state('expanded', style({
        width: '250px',
      })),
      state('collapsed', style({
        width: '60px',
      })),
      transition('expanded <=> collapsed', animate('200ms ease-in-out'))
    ])
  ]
})
export class SidebarComponent implements OnInit {
  dashboardType: string = '';

  @Output() sidebarToggled = new EventEmitter<boolean>();
  private readonly _authService = inject(AuthService);

  panelMenuItems: MenuItem[] = [];
  searchControl = new FormControl('');
  searchResults: MenuItem[] = [];
  isSearching: boolean = false;
  isSidebarExpanded: boolean = true;
  isMobile: boolean = window.innerWidth < 768;
  isDrawerOpen: boolean = false;
  isClosing: boolean = false;

  constructor(
    private router: Router,
    private studentSidebarService: StudentSidebarService,
    private adminSidebarService: AdminSidebarService
  ) { }

  ngOnInit() {
    this.dashboardType = this.extractDashboardTypeFromUrl();
    this.isMobile = window.innerWidth < 768;
    this.isSidebarExpanded = !this.isMobile;
    if (this.isMobile) {
      this.isDrawerOpen = false;
    } else {
      this.isSidebarExpanded = true;
    }

    if (this.dashboardType === 'Student') {
      this.panelMenuItems = this.studentSidebarService.getMenuItems();
    } else if (this.dashboardType === 'Admin') {
      this.panelMenuItems = this.adminSidebarService.getMenuItems();
    } else {
      this.panelMenuItems = this.studentSidebarService.getMenuItems();
    }

    this.searchControl.valueChanges.subscribe(value => {
      if (value) {
        this.isSearching = true;
        this.searchResults = this.search(value);
      } else {
        this.isSearching = false;
        this.searchResults = [];
      }
    });
  }

  extractDashboardTypeFromUrl(): string {
    const segments = this.router.url.split('/');
    if (segments.length > 1 && segments[1]) {
      const segment = segments[1].toLowerCase();
      if (segment === 'dashboard') {
        return 'Student';
      }
      return segment.charAt(0).toUpperCase() + segment.slice(1);
    }
    return 'Portal';
  }

  toggleMenu(item: MenuItem, event: Event) {
    event.preventDefault();
    if (item.items) {
      item.expanded = !item.expanded;
    }
  }

  search(text: string): MenuItem[] {
    const results: MenuItem[] = [];
    for (const item of this.panelMenuItems) {
      this.searchRecursive(item, text, results);
    }
    return results;
  }

  searchRecursive(item: MenuItem, text: string, results: MenuItem[]) {
    if (item.label.toLowerCase().includes(text.toLowerCase())) {
      results.push(item);
    }
    if (item.items) {
      for (const subItem of item.items) {
        this.searchRecursive(subItem, text, results);
      }
    }
  }

  clearSearch() {
    this.searchControl.setValue('');
    this.isSearching = false;
    this.searchResults = [];
  }

  logout() {
    this._authService.logout();
  }

  toggleSidebar() {
    if (this.isMobile) {
      if (this.isDrawerOpen) {
        this.isClosing = true;
        setTimeout(() => {
          this.isDrawerOpen = false;
          this.isClosing = false;
        }, 300);
      } else {
        this.isDrawerOpen = true;
      }
    }
  }

  navigateToSearchResult(item: MenuItem, event: Event) {
    if (item.items && item.items.length > 0) {
      event.preventDefault();
      item.expanded = !item.expanded;
    } else {
      this.clearSearch();
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.isMobile = window.innerWidth < 768;
    this.isSidebarExpanded = !this.isMobile;
  }
}
