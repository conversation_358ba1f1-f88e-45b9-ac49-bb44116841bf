<!-- Sidebar container -->
<div class="sidebar" 
     [ngClass]="{'mobile-drawer': isMobile, 'closing': isClosing}"
     [@sidebarAnimation]="isSidebarExpanded ? 'expanded' : 'collapsed'" 
     *ngIf="!isMobile || (isMobile && (isDrawerOpen || isClosing))">
    <!-- Wrap scrollable content -->
    <div class="sidebar-content">
        <!-- Logo -->
        <div class="logo-section">
            <img src="assets/images/aat_logo.webp" alt="Company Logo" class="logo-image cursor-pointer" routerLink="/">
            <span class="company-name">{{ dashboardType }} Portal</span>
        </div>
        <!-- Add search bar -->
        <div class="sidebar-search">
            <div class="search-box">
                <i class="pi pi-search"></i>
                <input [formControl]="searchControl" type="text" pInputText placeholder="Search menu..." />
                <i *ngIf="searchControl.value" class="pi pi-times clear-search" (click)="clearSearch()"></i>
            </div>
        </div>
        <!-- Navigation Menu -->
        <nav>
            <!-- Show search results when searching -->
            <ng-container *ngIf="isSearching && searchResults.length">
                <div class="search-results">
                    <div class="search-header">Search Results</div>
                    <ng-container *ngFor="let item of searchResults">
                        <a [routerLink]="item.routerLink" class="nav-item"
                            [class.has-submenu]="item.items && item.items.length > 0"
                            (click)="navigateToSearchResult(item, $event)">
                            <i [class]="item.icon"></i>
                            <span>{{ item.label }}</span>
                            <i *ngIf="item.items?.length" [class]="
                'pi ml-auto text-xs ' +
                (item.expanded ? 'pi-chevron-down' : 'pi-chevron-right')
              "></i>
                        </a>
                        <div *ngIf="item.items" class="submenu" [class.hidden]="!item.expanded"
                            [@slideInOut]="item.expanded ? 'in' : 'out'">
                            <ng-container *ngTemplateOutlet="
                menuItems;
                context: { $implicit: item.items }
              "></ng-container>
                        </div>
                    </ng-container>
                </div>
            </ng-container>
            <!-- Show regular menu when not searching -->
            <ng-container *ngIf="!isSearching">
                <ng-container *ngFor="let item of panelMenuItems">
                    <a [routerLink]="item.routerLink" class="nav-item"
                        [class.has-submenu]="item.items && item.items.length > 0"
                        (click)="item.items?.length ? toggleMenu(item, $event) : null">
                        <i [class]="item.icon"></i>
                        <span>{{ item.label }}</span>
                        <i *ngIf="item.items?.length" [class]="
              'pi ml-auto text-xs ' +
              (item.expanded ? 'pi-chevron-down' : 'pi-chevron-right')
            "></i>
                    </a>
                    <div *ngIf="item.items" class="submenu" [class.hidden]="!item.expanded"
                        [@slideInOut]="item.expanded ? 'in' : 'out'">
                        <ng-container *ngTemplateOutlet="
              menuItems;
              context: { $implicit: item.items }
            "></ng-container>
                    </div>
                </ng-container>
            </ng-container>
        </nav>
    </div>
    <!-- Bottom Controls (outside scrollable area) -->
    <div class="sidebar-controls">
        <!-- home button -->
        <div class="home-button-container">
            <p-button label="Home" icon="pi pi-home" variant="outlined" [routerLink]="['/']" severity="info">
            </p-button>
        </div>

        <!-- logout button -->
        <p-button label="Log Out" icon="pi pi-sign-out" severity="danger" variant="outlined"
            (onClick)="logout()"></p-button>
    </div>
</div>

<!-- Mobile overlay -->
<div class="mobile-overlay" 
     *ngIf="isMobile ? (isDrawerOpen || isClosing) : false" 
     [ngClass]="{'closing': isClosing}" 
     (click)="toggleSidebar()"></div>

<!-- Floating toggle button (only on mobile) -->
<div class="retoggle-button" *ngIf="isMobile && !isDrawerOpen && !isClosing" (click)="toggleSidebar()">
    <p-button icon="pi pi-angle-right" [rounded]="true"
        [style]="{ background: '#188bdb', 'border-color': '#188bdb' }"></p-button>
</div>

<!-- Recursive Menu Template -->
<ng-template #menuItems let-items>
    <ng-container *ngFor="let item of items">
        <a [routerLink]="item.routerLink" class="nav-item" [class.has-submenu]="item.items?.length"
            (click)="item.items?.length ? toggleMenu(item, $event) : null">
            <i [class]="item.icon"></i>
            <span>{{ item.label }}</span>
            <i *ngIf="item.items?.length" [class]="
          'pi ml-auto text-xs ' +
          (item.expanded ? 'pi-chevron-down' : 'pi-chevron-right')
        "></i>
        </a>
        <div *ngIf="item.items" class="submenu" [class.hidden]="!item.expanded"
            [@slideInOut]="item.expanded ? 'in' : 'out'">
            <ng-container *ngTemplateOutlet="menuItems; context: { $implicit: item.items }"></ng-container>
        </div>
    </ng-container>
</ng-template>