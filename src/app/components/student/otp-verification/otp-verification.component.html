<div
    class="fixed inset-0 w-full h-full min-h-screen bg-[url('/assets/images/NewSlideThree.webp')] bg-cover bg-center bg-no-repeat">
    <p-toast></p-toast>
    <div class="absolute inset-0 w-full h-full bg-gradient-to-r from-[#198BDB] to-[#262364] opacity-90"></div>
    <div
        class="relative z-10 flex flex-col md:flex-row items-center justify-center w-full h-full min-h-screen mx-auto px-4 md:px-16 max-w-7xl">

        <!-- Verification Card -->
        <p-card *ngIf="currentView === 'verify'"
            styleClass="otp-card shadow-lg rounded-xl w-full sm:max-w-md md:min-w-[500px] md:max-w-[500px]">
            <ng-template pTemplate="header">
                <div class="text-center py-5 bg-blue-50 rounded-t-xl">
                    <i class="pi pi-shield text-4xl text-primary-blue mb-3"></i>
                    <h1 class="font-bold text-2xl text-blue-900">Verify Your Account</h1>
                </div>
            </ng-template>
            <div class="flex flex-col items-center p-5">
                <div class="text-center mb-5">
                    <p class="text-gray-600 text-sm mb-2">
                        Please enter the 6-digit verification code sent to: <span
                            class="text-primary-blue font-medium">{{ email }}</span>
                    </p>
                    <a (click)="goBackToAuth()"
                        class="text-xs text-primary-blue hover:underline cursor-pointer mt-2 inline-block">
                        Not your email?
                    </a>
                </div>
                <p-inputotp [(ngModel)]="otpCode" [length]="6" styleClass="mb-5">
                    <ng-template #input let-token let-events="events" let-index="index">
                        <input type="text" [maxLength]="1" (input)="events.input($event)"
                            (keydown)="events.keydown($event)" [attr.value]="token" class="custom-otp-input" />
                        <div *ngIf="index === 3" class="px-4">
                        </div>
                    </ng-template>
                </p-inputotp>
                <div class="flex justify-between mt-8 self-stretch w-full">
                    <p-button [label]="resendDisabled ? 'Resend in ' + resendCountdown + 's' : 'Resend Code'"
                        [link]="true" [disabled]="resendDisabled" (onClick)="resendCode()"
                        styleClass="text-primary-blue hover:text-primary-blue-hover" />
                    <p-button label="Verify Code" severity="primary" [loading]="isSubmitting" (onClick)="submitCode()"
                        styleClass="bg-primary-blue hover:bg-primary-blue-hover text-white" />
                </div>
            </div>
            <ng-template pTemplate="footer">
                <div class="text-center text-sm text-gray-500 pt-2 pb-4">
                    Having trouble? Please contact <a href="#"
                        class="text-primary-blue hover:text-primary-blue-hover hover:underline">support</a>
                </div>
            </ng-template>
        </p-card>

        <!-- Success Card -->
        <p-card *ngIf="currentView === 'success'"
            styleClass="otp-card shadow-lg rounded-xl success-card w-full sm:max-w-md md:min-w-[500px] md:max-w-[500px]">
            <ng-template pTemplate="header">
                <div class="text-center py-5 bg-green-50 rounded-t-xl">
                    <i class="pi pi-check-circle text-5xl text-primary-green mb-3 animate-bounce"></i>
                    <h1 class="font-bold text-2xl text-green-900">Verification Successful</h1>
                </div>
            </ng-template>
            <div class="flex flex-col items-center p-6">
                <p class="text-gray-600 text-center mb-3">
                    Your account has been successfully verified.
                </p>
                <p class="text-gray-500 text-center text-sm">
                    Redirecting to dashboard in 3 seconds...
                </p>
                <div class="w-full mt-6">
                    <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div class="progress-bar h-full bg-primary-blue"></div>
                    </div>
                </div>
            </div>
        </p-card>

        <!-- Error Card -->
        <p-card *ngIf="currentView === 'error'"
            styleClass="otp-card shadow-lg rounded-xl error-card w-full sm:max-w-md md:min-w-[500px] md:max-w-[500px]">
            <ng-template pTemplate="header">
                <div class="text-center py-5 bg-red-50 rounded-t-xl">
                    <i class="pi pi-times-circle text-5xl text-primary-red mb-3 animate-pulse"></i>
                    <h1 class="font-bold text-2xl text-red-900">Verification Failed</h1>
                </div>
            </ng-template>
            <div class="flex flex-col items-center p-6">
                <p class="text-gray-600 text-center mb-6">
                    {{ errorMessage }}
                </p>
                <div class="text-sm text-gray-500 text-center mb-6">
                    <p>If you're having trouble verifying your account:</p>
                    <ul class="list-disc text-left pl-8 mt-2">
                        <li>Make sure you're entering the correct code</li>
                        <li>Check if the code has expired</li>
                        <li>Ensure you're using the email you registered with</li>
                    </ul>
                </div>
                <p-button label="Try Again" (onClick)="resetView()"
                    styleClass="bg-primary-blue hover:bg-primary-blue-hover text-white" />
            </div>
            <ng-template pTemplate="footer">
                <div class="text-center text-sm text-gray-500 pt-2 pb-4">
                    Having trouble? Please contact <a href="#"
                        class="text-primary-blue hover:text-primary-blue-hover hover:underline">support</a>
                </div>
            </ng-template>
        </p-card>
    </div>
</div>