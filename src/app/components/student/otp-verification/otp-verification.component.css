.otp-card {
    min-width: 350px;
    max-width: 450px;
    width: 100%;
    border-radius: 1rem;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.otp-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.p-inputotp {
    gap: 0;
    display: flex;
    justify-content: center;
    width: 100%;
}

.custom-otp-input {
    width: 48px;
    height: 48px;
    font-size: 24px;
    font-weight: 500;
    appearance: none;
    text-align: center;
    border-radius: 0;
    border: 1px solid var(--p-inputtext-border-color, #ced4da);
    background: transparent;
    outline-offset: -2px;
    outline-color: transparent;
    border-right: 0 none;
    transition: all 0.2s, outline-color 0.3s, box-shadow 0.3s, border-color 0.3s;
    color: var(--p-inputtext-color, #495057);
}

.custom-otp-input:focus {
    outline: 2px solid var(--p-focus-ring-color, #2196F3);
    box-shadow: 0 0 0 2px #bbdefb;
    z-index: 1;
}

.custom-otp-input:first-child,
.custom-otp-input:nth-child(5) {
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
}

.custom-otp-input:nth-child(3),
.custom-otp-input:last-child {
    border-top-right-radius: 12px;
    border-bottom-right-radius: 12px;
    border-right-width: 1px;
    border-right-style: solid;
    border-color: var(--p-inputtext-border-color, #ced4da);
}

/* Animation for inputs */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.custom-otp-input:focus {
    animation: pulse 0.3s ease-in-out;
}

/* Success and error card animations */
.success-card, .error-card {
    animation: fadeInDown 0.5s ease-out;
}

.success-card .pi-check-circle {
    animation: bounce 1s infinite;
}

.error-card .pi-times-circle {
    animation: pulse 1.5s infinite;
}

/* Progress bar animation */
.progress-bar {
    animation: progressFill 3s linear forwards;
    width: 0;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes progressFill {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .otp-card {
        min-width: 300px;
    }
    
    .custom-otp-input {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
}