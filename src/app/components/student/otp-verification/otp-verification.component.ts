import { Component, inject, OnInit } from '@angular/core';
import { InputOtpModule } from 'primeng/inputotp';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../services/auth/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';

@Component({
  selector: 'app-otp-verification',
  standalone: true,
  imports: [
    InputOtpModule,
    CardModule,
    ButtonModule,
    FormsModule,
    CommonModule,
    ToastModule,
  ],
  templateUrl: './otp-verification.component.html',
  styleUrl: './otp-verification.component.css',
  providers: [MessageService],
})
export class OtpVerificationComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly messageService = inject(MessageService);

  email: string = '';
  otpCode: string = '';
  isSubmitting: boolean = false;
  resendDisabled: boolean = false;
  resendCountdown: number = 0;

  // State management for different cards
  currentView: 'verify' | 'success' | 'error' = 'verify';
  errorMessage: string = '';

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['email']) {
        this.email = params['email'];
      } else {
        this.router.navigate(['/auth']);
      }
    });
  }

  submitCode(): void {
    if (!this.otpCode || this.otpCode.length !== 6) {
      this.errorMessage = 'Please enter a valid 6-digit code';
      this.currentView = 'error';
      return;
    }
    this.isSubmitting = true;

    this.authService.getOtp(this.email, this.otpCode).subscribe({
      next: () => {
        this.currentView = 'success';
        setTimeout(() => {
          this.router.navigate(['/dashboard']);
        }, 3000);
      },
      error: (error) => {
        this.currentView = 'error';
        this.errorMessage =
          error?.error?.message ?? 'Verification failed. Please try again.';
        this.isSubmitting = false;
      },
      complete: () => {
        this.isSubmitting = false;
      },
    });
  }

  resendCode(): void {
    if (this.resendDisabled) return;
    this.resendDisabled = true;
    this.resendCountdown = 30;

    this.authService.resendOtp(this.email).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Code Resent',
          detail: `A new verification code has been sent to ${this.email}. Please check your inbox.`,
        });
      },
      error: (error) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail:
            error?.error?.message ?? 'Failed to resend code. Please try again.',
        });
      },
      complete: () => {
        this.resendDisabled = false;
      },
    });

    const interval = setInterval(() => {
      this.resendCountdown--;
      if (this.resendCountdown <= 0) {
        this.resendDisabled = false;
        clearInterval(interval);
      }
    }, 1000);
  }

  resetView(): void {
    if (
      this.errorMessage.includes('not found') ||
      this.errorMessage.includes('expired')
    ) {
      this.router.navigate(['/auth']);
    } else {
      this.currentView = 'verify';
      this.errorMessage = '';
      this.otpCode = '';
    }
  }

  goBackToAuth(): void {
    this.router.navigate(['/auth']);
  }
}
