import { CommonModule } from '@angular/common';
import {
  Component,
  AfterViewInit,
  ElementRef,
  ViewChild,
  OnInit,
  inject,
} from '@angular/core';
import { ProgressBarModule } from 'primeng/progressbar';
import { Chart, registerables } from 'chart.js';
import { AuthService } from '../../../services/auth/auth.service';
Chart.register(...registerables);

@Component({
  selector: 'app-dashboard-home',
  standalone: true,
  imports: [CommonModule, ProgressBarModule],
  templateUrl: './dashboard-home.component.html',
  styleUrls: ['./dashboard-home.component.css'],
})
export class DashboardHomeComponent implements OnInit, AfterViewInit {
  @ViewChild('gradesChart') gradesChartRef!: ElementRef;

  private readonly _authService = inject(AuthService);
  public gradesChart: any;
  public userName: string = 'Malith Perera';

  announcements = [
    'New tax accounting guidelines updated.',
    'Business ethics seminar next week.',
    'Corporate finance workshop this Friday.',
  ];

  deadlines = [
    'Financial Report - Feb 25',
    'Business Plan - Mar 5',
    'Audit Project - Apr 15',
  ];

  courses = [
    { name: 'Financial Accounting', progress: 60 },
    { name: 'Business Management', progress: 75 },
    { name: 'Corporate Finance', progress: 40 },
  ];

  grades = [
    { course: 'Financial Accounting', grade: 'B+' },
    { course: 'Business Management', grade: 'A-' },
    { course: 'Corporate Finance', grade: 'C+' },
  ];

  calendarEvents = [
    { event: 'Tax Analysis Report Due', date: 'Feb 25' },
    { event: 'Management Case Study', date: 'Mar 1' },
    { event: 'Financial Planning Presentation', date: 'Mar 10' },
  ];

  ngOnInit(): void {
    const username = this._authService.getCurrentUser()?.username || '';
    this.userName = username.charAt(0).toUpperCase() + username.slice(1);
  }

  ngAfterViewInit(): void {
    this.createChart();
  }

  createChart() {
    // Convert letter grades to numerical values
    const gradeValues = this.grades.map((item) => {
      const gradeMap: { [key: string]: number } = {
        'A+': 95,
        A: 90,
        'A-': 85,
        'B+': 80,
        B: 75,
        'B-': 70,
        'C+': 65,
        C: 60,
        'C-': 55,
      };
      return gradeMap[item.grade] || 0;
    });
    const courseLabels = this.grades.map((item) => item.course);

    this.gradesChart = new Chart(this.gradesChartRef.nativeElement, {
      type: 'bar',
      data: {
        labels: courseLabels,
        datasets: [
          {
            label: 'Course Grades',
            data: gradeValues,
            backgroundColor: [
              'rgba(100, 149, 237, 0.6)',
              'rgba(147, 112, 219, 0.6)',
              'rgba(144, 238, 144, 0.6)',
            ],
            borderColor: [
              'rgba(100, 149, 237, 1)',
              'rgba(147, 112, 219, 1)',
              'rgba(144, 238, 144, 1)',
            ],
            borderWidth: 2,
            borderRadius: 5,
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'top',
          },
          title: {
            display: true,
            text: 'Course Performance',
          },
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            title: {
              display: true,
              text: 'Grade (%)',
            },
          },
        },
      },
    });
  }
}
