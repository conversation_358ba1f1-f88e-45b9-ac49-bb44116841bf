import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MenuItem, MessageService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { TrainingSupervisorService } from '../../../../services/student/training/training-supervisor.service';
import { SupervisorRegistration, SupervisorRegistrationResponse } from '../../../../models/shared/supervisor/supervisor';

@Component({
  selector: 'app-training-supervisor',
  imports: [
    CommonModule,
    ButtonModule,
    CardModule,
    TagModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
  ],
  providers: [MessageService],
  templateUrl: './training-supervisor.component.html',
  styleUrl: './training-supervisor.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingSupervisorComponent implements OnInit, OnDestroy {
  private readonly trainingSupervisorService = inject(
    TrainingSupervisorService
  );
  private readonly messageService = inject(MessageService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();

  hasRegistration = false;
  supervisorData: SupervisorRegistration | null = null;
  currentUsername = 'current_user'; // This should come from auth service

  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.checkSupervisorRegistration();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/' };
    this.breadcrumbItems = [
      { label: 'Training' },
      { label: 'Supervisor Registration' },
    ];
  }

  private checkSupervisorRegistration(): void {
    this.cdr.markForCheck();

    this.trainingSupervisorService
      .checkSupervisorRegistrationStatus(this.currentUsername)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (response: SupervisorRegistrationResponse) => {
          this.hasRegistration = response.hasRegistration;
          if (this.hasRegistration && response.data) {
            this.supervisorData = response.data;
          }
          this.cdr.markForCheck();
        },
        error: (error: any) => {
          console.error('Error checking supervisor registration:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load supervisor registration status',
          });
        },
      });
  }

  onAddSupervisor(): void {
    this.router.navigate(['/dashboard/supervisor-registration']);
  }

  getStatusSeverity(status: string): 'success' | 'warn' | 'danger' | 'info' {
    switch (status) {
      case 'approved':
        return 'success';
      case 'pending':
        return 'warn';
      case 'rejected':
        return 'danger';
      default:
        return 'info';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'approved':
        return 'pi pi-check-circle';
      case 'pending':
        return 'pi pi-clock';
      case 'rejected':
        return 'pi pi-times-circle';
      default:
        return 'pi pi-info-circle';
    }
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
}
