<p-toast></p-toast>

<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-3xl">Training Supervisor</span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">

    <!-- No Registration -->
    <div *ngIf="!hasRegistration" class="max-w-2xl mx-auto text-center">
        <div class="p-8 md:p-12">
            <div class="mb-6">
                <div class="w-20 h-20 mx-auto bg-blue-50 rounded-full flex items-center justify-center">
                    <i class="pi pi-user-plus text-3xl text-blue-600"></i>
                </div>
            </div>

            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">No Supervisor Registered</h2>
                <p class="text-gray-600 leading-relaxed">
                    You haven't registered a training supervisor yet. A training supervisor is required to guide
                    and monitor your practical training experience.
                </p>
            </div>

            <div class="space-y-4">
                <p-button (onClick)="onAddSupervisor()" icon="pi pi-plus" label="Register Supervisor"
                    styleClass="p-button-primary p-button-lg w-full md:w-auto" [style]="{ 'min-width': '200px' }">
                </p-button>

                <p class="text-sm text-gray-500 mt-4">
                    <i class="pi pi-info-circle mr-1"></i>
                    Complete the supervisor registration form with your mentor's details
                </p>
            </div>
        </div>
    </div>

    <!-- Existing Registration -->
    <div *ngIf="hasRegistration && supervisorData" class="max-w-4xl mx-auto">
        <p-card>
            <ng-template pTemplate="header">
                <div class="flex justify-between items-center p-4 border-b border-gray-200">
                    <div class="flex items-center gap-3">
                        <i class="pi pi-user text-2xl text-blue-600"></i>
                        <h2 class="text-xl font-semibold text-gray-900">Supervisor Details</h2>
                    </div>
                </div>
            </ng-template>

            <ng-template pTemplate="content">
                <div class="p-4">
                    <!-- Status Banner -->
                    <div class="mb-6 p-4 rounded-lg border" [ngClass]="{
                 'bg-green-50 border-green-200': supervisorData.approvalStatus === 'approved',
                 'bg-yellow-50 border-yellow-200': supervisorData.approvalStatus === 'pending',
                 'bg-red-50 border-red-200': supervisorData.approvalStatus === 'rejected'
               }">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i [class]="getStatusIcon(supervisorData.approvalStatus)" [ngClass]="{
                     'text-green-600': supervisorData.approvalStatus === 'approved',
                     'text-yellow-600': supervisorData.approvalStatus === 'pending',
                     'text-red-600': supervisorData.approvalStatus === 'rejected'
                   }"></i>
                                <div>
                                    <h3 class="font-semibold" [ngClass]="{
                        'text-green-800': supervisorData.approvalStatus === 'approved',
                        'text-yellow-800': supervisorData.approvalStatus === 'pending',
                        'text-red-800': supervisorData.approvalStatus === 'rejected'
                      }">
                                        Registration {{ supervisorData.approvalStatus | titlecase }}
                                    </h3>
                                    <p class="text-sm" [ngClass]="{
                       'text-green-600': supervisorData.approvalStatus === 'approved',
                       'text-yellow-600': supervisorData.approvalStatus === 'pending',
                       'text-red-600': supervisorData.approvalStatus === 'rejected'
                     }">
                                        <span *ngIf="supervisorData.approvalStatus === 'approved'">
                                            Your supervisor registration has been approved
                                        </span>
                                        <span *ngIf="supervisorData.approvalStatus === 'pending'">
                                            Your supervisor registration is under review
                                        </span>
                                        <span *ngIf="supervisorData.approvalStatus === 'rejected'">
                                            Your supervisor registration has been rejected
                                        </span>
                                    </p>
                                </div>
                            </div>
                            <p-tag [value]="supervisorData.approvalStatus | titlecase"
                                [severity]="getStatusSeverity(supervisorData.approvalStatus)"
                                [icon]="getStatusIcon(supervisorData.approvalStatus)">
                            </p-tag>
                        </div>

                        <!-- Rejection Reason -->
                        <div *ngIf="supervisorData.approvalStatus === 'rejected' && supervisorData.rejectionReason"
                            class="mt-3 p-3 bg-red-100 border border-red-200 rounded">
                            <p class="text-sm font-medium text-red-800 mb-1">Rejection Reason:</p>
                            <p class="text-sm text-red-700">{{ supervisorData.rejectionReason }}</p>
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <h4 class="font-semibold text-lg text-gray-900 border-b border-gray-200 pb-2">
                                Personal Information
                            </h4>
                            <div class="space-y-3">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-500 mb-1">Name</span>
                                    <p class="text-gray-900">{{ supervisorData.name }}</p>
                                </div>

                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-500 mb-1">Designation</span>
                                    <p class="text-gray-900">{{ supervisorData.designation }}</p>
                                </div>

                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-500 mb-1">Location</span>
                                    <p class="text-gray-900">{{ supervisorData.location }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <h4 class="font-semibold text-lg text-gray-900 border-b border-gray-200 pb-2">
                                Contact Information
                            </h4>
                            <div class="space-y-3">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-500 mb-1">Email</span>
                                    <p class="text-gray-900">{{ supervisorData.email }}</p>
                                </div>

                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-500 mb-1">Contact Number</span>
                                    <p class="text-gray-900">{{ supervisorData.contactNumber }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Professional Qualifications -->
                    <div class="mt-6">
                        <h4 class="font-semibold text-lg text-gray-900 border-b border-gray-200 pb-2 mb-4">
                            Professional Memberships
                        </h4>
                        <div class="flex flex-wrap gap-2">
                            <p-tag *ngFor="let membership of supervisorData.memberships" [value]="membership"
                                severity="info" styleClass="text-sm">
                            </p-tag>
                            <span *ngIf="supervisorData.memberships.length === 0" class="text-gray-500 text-sm">
                                No memberships specified
                            </span>
                        </div>
                    </div>

                    <!-- Registration Timeline -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="font-semibold text-lg text-gray-900 mb-4">Registration Timeline</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div class="flex items-center gap-2">
                                <i class="pi pi-calendar text-gray-500"></i>
                                <span class="text-gray-500">Registered:</span>
                                <span class="text-gray-900 font-medium">{{ formatDate(supervisorData.registeredAt)
                                    }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-template>
        </p-card>
    </div>
</div>