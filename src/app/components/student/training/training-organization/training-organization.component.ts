import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { MenuItem, MessageService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { TrainingOrganizationService } from '../../../../services/student/training/training-organization.service';
import { TrainingOrganizationViewData } from '../../../../models/shared/training/training-organization.model';

@Component({
  selector: 'app-training-organization',
  imports: [
    CommonModule,
    ButtonModule,
    CardModule,
    TagModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    TableModule,
    DialogModule,
  ],
  providers: [MessageService],
  templateUrl: './training-organization.component.html',
  styleUrl: './training-organization.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingOrganizationComponent implements OnInit, OnDestroy {
  private readonly trainingOrganizationService = inject(
    TrainingOrganizationService
  );
  private readonly messageService = inject(MessageService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly destroy$ = new Subject<void>();

  // Component state
  hasRegistration = false;
  organizationData: TrainingOrganizationViewData | null = null;

  // Table state
  organizations: TrainingOrganizationViewData[] = [];
  isLoadingOrganizations = false;

  // Pagination state
  pageNumber = 1;
  pageSize = 10;
  totalRecords = 0;

  // Dialog state
  showDetailsDialog = false;
  selectedOrganization: TrainingOrganizationViewData | null = null;

  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};
  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.loadUserOrganizations();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/' };
    this.breadcrumbItems = [
      { label: 'Training' },
      { label: 'Organization Registration' },
    ];
  }

  private loadUserOrganizations(): void {
    this.isLoadingOrganizations = true;
    this.cdr.markForCheck();

    this.trainingOrganizationService
      .getTrainingOrganizationsAsyncCreatedByUserId(
        this.pageNumber,
        this.pageSize
      )
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoadingOrganizations = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (response) => {
          this.organizations = response.body || [];
          this.hasRegistration = this.organizations.length > 0;
          this.cdr.markForCheck();
        },
        error: (error: any) => {
          console.error('Error loading organizations:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load organizations',
          });
          this.hasRegistration = false;
          this.organizations = [];
        },
      });
  }

  onAddOrganization(): void {
    this.router.navigate([
      '/dashboard/training/training-organization-registration',
    ]);
  }

  onEditOrganization(): void {
    if (this.organizationData) {
      this.router.navigate(
        ['/dashboard/training/training-organization-registration'],
        {
          queryParams: { edit: true, id: this.organizationData.organizationId },
        }
      );
    }
  }

  // Dialog methods
  onViewDetails(organization: TrainingOrganizationViewData): void {
    this.selectedOrganization = organization;
    this.showDetailsDialog = true;
    this.cdr.markForCheck();
  }

  onCloseDetailsDialog(): void {
    this.showDetailsDialog = false;
    this.selectedOrganization = null;
    this.cdr.markForCheck();
  }

  onEditFromTable(organization: TrainingOrganizationViewData): void {
    this.router.navigate(
      ['/dashboard/training/training-organization-registration'],
      {
        queryParams: { edit: true, id: organization.organizationId },
      }
    );
  }

  getStatusSeverity(status: string): 'success' | 'warn' | 'danger' | 'info' {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'success';
      case 'pending approvel':
        return 'warn';
      case 'rejected':
        return 'danger';
      default:
        return 'info';
    }
  }

  getTrainingSchemaSeverity(
    trainingSchema: string
  ): 'secondary' | 'info' | 'warn' | 'danger' {
    switch (trainingSchema?.toLowerCase()) {
      case 'monitored':
        return 'info';
      case 'non-monitored':
        return 'warn';
      case 'hybrid':
        return 'secondary';
      case 'notdefined':
        return 'danger';
      default:
        return 'info';
    }
  }
}
