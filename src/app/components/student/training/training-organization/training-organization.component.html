<p-toast></p-toast>

<!-- Header Section -->
<div class="w-full px-4 py-4">
  <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
    <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
      <span class="text-white font-semibold text-3xl">Training Organization</span>
      <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">

  <!-- No Registration State -->
  <div *ngIf="!hasRegistration" class="max-w-2xl mx-auto text-center">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 md:p-12">
      <!-- Icon -->
      <div class="mb-6">
        <div class="w-20 h-20 mx-auto bg-blue-50 rounded-full flex items-center justify-center">
          <i class="pi pi-building text-3xl text-blue-600"></i>
        </div>
      </div>

      <!-- Content -->
      <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">No Training Organization Registered</h2>
        <p class="text-gray-600 leading-relaxed">
          You haven't registered a training organization yet. A training organization is required to provide
          practical training and professional development opportunities.
        </p>
      </div>

      <!-- Call to Action -->
      <div class="space-y-4">
        <p-button (onClick)="onAddOrganization()" icon="pi pi-plus" label="Register Organization"
          styleClass="p-button-primary p-button-lg w-full md:w-auto" [style]="{ 'min-width': '200px' }">
        </p-button>

        <p class="text-sm text-gray-500 mt-4">
          <i class="pi pi-info-circle mr-1"></i>
          Complete the organization registration form with your institution's details
        </p>
      </div>
    </div>
  </div>

  <!-- Organization Management Table -->
  <div *ngIf="hasRegistration" class="max-w-7xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- Header with Add Button -->
      <div class="p-4 sm:p-6 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 class="text-xl font-semibold text-gray-900">Your Training Organizations</h2>
            <p class="text-sm text-gray-600 mt-1">Manage your registered training organizations</p>
          </div>
          <p-button (onClick)="onAddOrganization()" icon="pi pi-plus" label="Add Organization"
            styleClass="p-button-primary">
          </p-button>
        </div>
      </div> <!-- Organizations Table -->
      <div *ngIf="!isLoadingOrganizations; else loadingTemplate">
        <p-table [value]="organizations" styleClass="p-datatable-striped" [paginator]="totalRecords > pageSize"
          [rows]="pageSize" [totalRecords]="totalRecords" [lazy]="true" [showCurrentPageReport]="true"
          currentPageReportTemplate="Showing {first} to {last} of {totalRecords} organizations">

          <!-- Organization Name Column -->
          <ng-template pTemplate="header">
            <tr>
              <th scope="col" style="width:25%" class="text-left py-3 px-4 font-semibold text-gray-900 border-b border-gray-200">
                Organization Name
              </th>
                <th scope="col" style="width:20%" class="text-center py-3 px-4 font-semibold text-gray-900 border-b border-gray-200">
                Training Schema
                </th>
              <th scope="col" style="width:20%" class="text-left py-3 px-4 font-semibold text-gray-900 border-b border-gray-200">
                Email
              </th>
              <th scope="col" style="width:20%" class="text-center py-3 px-4 font-semibold text-gray-900 border-b border-gray-200">
                Status
              </th>
              <th scope="col" style="width:20%" class="text-center py-3 px-4 font-semibold text-gray-900 border-b border-gray-200">
                Actions
              </th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-organization>
            <tr class="hover:bg-gray-50 transition-colors"> <!-- Organization Name -->
              <td class="py-4 px-4 border-b border-gray-100">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="pi pi-building text-blue-600"></i>
                  </div>
                  <div>
                    <p class="font-medium text-gray-900">{{ organization.organizationName }}</p>
                  </div>
                </div>
              </td>

              <!-- Training Schema -->
              <td class="py-4 px-4 border-b border-gray-100 text-center">
                <div class="flex">
                  <p-tag [value]="organization.trainingSchema | titlecase"
                    [severity]="getTrainingSchemaSeverity(organization.trainingSchema)">
                  </p-tag>
                </div>
              </td>

              <!-- Email -->
              <td class="py-4 px-4 border-b border-gray-100">
                <p class="text-gray-900">{{ organization.organizationEmail }}</p>
              </td>

              <!-- Status -->
              <td class="py-4 px-4 border-b border-gray-100 text-center">
                <div class="flex">
                  <p-tag [value]="organization.approvedStatus | titlecase"
                    [severity]="getStatusSeverity(organization.approvedStatus)">
                  </p-tag>
                </div>
              </td>

              <!-- Actions -->
              <td class="py-4 px-4 border-b border-gray-100 text-center">
                <p-button (onClick)="onViewDetails(organization)" icon="pi pi-eye" pTooltip="View Details"
                  tooltipPosition="top" styleClass="p-button-text p-button-sm p-button-rounded">
                </p-button>
              </td>
            </tr>
          </ng-template>

          <!-- Empty State -->
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="5" class="text-center py-12">
                <div class="flex flex-col items-center gap-4">
                  <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="pi pi-building text-2xl text-gray-400"></i>
                  </div>
                  <div>
                    <p class="text-gray-500 font-medium">No organizations found</p>
                    <p class="text-sm text-gray-400 mt-1">Start by registering your first organization</p>
                  </div>
                </div>
              </td>
            </tr>
          </ng-template> </p-table>
      </div>

      <!-- Loading Template -->
      <ng-template #loadingTemplate>
        <div class="text-center py-12">
          <p-progressSpinner [style]="{'width': '50px', 'height': '50px'}" strokeWidth="4"></p-progressSpinner>
          <p class="text-gray-500 mt-4">Loading organizations...</p>
        </div>
      </ng-template>
    </div>
  </div>

  <!-- Organization Details Dialog -->
  <p-dialog [(visible)]="showDetailsDialog" [modal]="true" [closable]="true" [draggable]="false" [resizable]="false"
    styleClass="w-full max-w-4xl mx-4" header="Organization Details" (onHide)="onCloseDetailsDialog()">

    <div *ngIf="selectedOrganization" class="p-0">
      <!-- Status Banner -->
      <div class="mb-6 p-4 rounded-lg border" [ngClass]="{
             'bg-green-50 border-green-200': selectedOrganization.approvedStatus === 'approved',
             'bg-yellow-50 border-yellow-200': selectedOrganization.approvedStatus === 'pending',
             'bg-red-50 border-red-200': selectedOrganization.approvedStatus === 'rejected'
           }">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div>
              <h3 class="font-semibold" [ngClass]="{
                    'text-green-800': selectedOrganization.approvedStatus === 'approved',
                    'text-yellow-800': selectedOrganization.approvedStatus === 'pending',
                    'text-red-800': selectedOrganization.approvedStatus === 'rejected'
                  }">
                Registration {{ selectedOrganization.approvedStatus | titlecase }}
              </h3>
              <p class="text-sm" [ngClass]="{
                   'text-green-600': selectedOrganization.approvedStatus === 'approved',
                   'text-yellow-600': selectedOrganization.approvedStatus === 'pending',
                   'text-red-600': selectedOrganization.approvedStatus === 'rejected'
                 }">
                <span *ngIf="selectedOrganization.approvedStatus === 'approved'">
                  Your organization registration has been approved
                </span>
                <span *ngIf="selectedOrganization.approvedStatus === 'pending'">
                  Your organization registration is under review
                </span>
                <span *ngIf="selectedOrganization.approvedStatus === 'rejected'">
                  Your organization registration has been rejected
                </span>
              </p>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <p-tag [value]="selectedOrganization.approvedStatus | titlecase"
              [severity]="getStatusSeverity(selectedOrganization.approvedStatus)">
            </p-tag>
          </div>
        </div>
      </div>

      <!-- Organization Information -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <h4 class="font-semibold text-lg text-gray-900 border-b border-gray-200 pb-2">
            Organization Information
          </h4>
          <div class="space-y-3">
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 mb-1">Organization Name</span>
              <p class="text-gray-900">{{ selectedOrganization.organizationName }}</p>
            </div>

            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 mb-1">Organization ID</span>
              <p class="text-gray-900">{{ selectedOrganization.strOrganizationId }}</p>
            </div>

            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 mb-1">Category</span>
              <p class="text-gray-900">{{ selectedOrganization.category }}</p>
            </div>

            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 mb-1">Training Schema</span>
              <p class="text-gray-900">{{ selectedOrganization.trainingSchema }}</p>
            </div>
          </div>
        </div>

        <div class="space-y-4">
          <h4 class="font-semibold text-lg text-gray-900 border-b border-gray-200 pb-2">
            Contact Information
          </h4>

          <div class="space-y-3">
            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 mb-1">Email</span>
              <p class="text-gray-900">{{ selectedOrganization.organizationEmail }}</p>
            </div>

            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 mb-1">Contact Number</span>
              <p class="text-gray-900">{{ selectedOrganization.contactNumber }}</p>
            </div>

            <div class="flex flex-col">
              <span class="text-sm font-medium text-gray-500 mb-1">Address</span>
              <p class="text-gray-900">{{ selectedOrganization.address }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Organization Qualifications -->
      <div class="mt-6" *ngIf="selectedOrganization.organizationQualification?.length">
        <h4 class="font-semibold text-lg text-gray-900 border-b border-gray-200 pb-2 mb-4">
          Organization Qualifications
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div *ngFor="let qualification of selectedOrganization.organizationQualification"
            class="p-4 border border-gray-200 rounded-lg">
            <div class="space-y-2">
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-500 mb-1">Employee Name</span>
                <p class="text-gray-900">{{ qualification.employeeName }}</p>
              </div>
              <div class="flex flex-col">
                <span class="text-sm font-medium text-gray-500 mb-1">Designation</span>
                <p class="text-gray-900">{{ qualification.employeeDesignation }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ng-template pTemplate="footer">
      <div class="flex justify-end gap-2">
        <p-button label="Edit Organization" icon="pi pi-pencil" (onClick)="onEditFromTable(selectedOrganization!)"
          styleClass="p-button-outlined">
        </p-button>
        <p-button label="Close" icon="pi pi-times" (onClick)="onCloseDetailsDialog()" styleClass="p-button-secondary">
        </p-button>
      </div>
    </ng-template>
  </p-dialog>
</div>