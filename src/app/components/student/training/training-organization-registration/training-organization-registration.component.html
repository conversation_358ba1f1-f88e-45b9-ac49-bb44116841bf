<p-toast></p-toast>

<!-- Header Section -->
<div class="w-full px-4 py-4">
    <div class="h-32 bg-gradient-to-r from-[#198BDB] to-[#262364] rounded-lg">
        <div class="flex flex-col h-full justify-center items-center px-4 pt-4 text-center">
            <span class="text-white font-semibold text-2xl sm:text-3xl">
                {{ isUpdateMode ? 'Update Organization Details' : 'Training Organization Registration' }}
            </span>
            <p-breadcrumb class="max-w-full" [model]="breadcrumbItems" [home]="home"></p-breadcrumb>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="px-4 pt-4 md:p-6 mx-auto max-w-7xl">
    <!-- Form Content -->
    <div class="max-w-4xl mx-auto">
        <form [formGroup]="organizationForm" (ngSubmit)="onSubmit()">
            <!-- Organization Information Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-building text-blue-600"></i>
                        Organization Information
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">Provide basic details about your training organization</p>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    <!-- Organization Name -->
                    <div>
                        <label for="organizationName" class="block text-sm font-medium text-gray-700 mb-2">
                            Organization Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="organizationName" pInputText formControlName="organizationName"
                            class="w-full h-11" placeholder="Enter organization name"
                            [class.ng-invalid]="isFieldInvalid('organizationName')"
                            [class.ng-dirty]="organizationForm.get('organizationName')?.dirty">
                        <small *ngIf="isFieldInvalid('organizationName')" class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('organizationName') }}
                        </small>
                    </div>

                    <!-- Organization ID -->
                    <div>
                        <label for="strOrganizationId" class="block text-sm font-medium text-gray-700 mb-2">
                            Business Registration Number <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="strOrganizationId" pInputText formControlName="strOrganizationId"
                            class="w-full h-11" placeholder="Enter the business number"
                            [class.ng-invalid]="isFieldInvalid('strOrganizationId')"
                            [class.ng-dirty]="organizationForm.get('strOrganizationId')?.dirty">
                        <small *ngIf="isFieldInvalid('strOrganizationId')" class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('strOrganizationId') }}
                        </small>
                    </div>

                    <!-- Address -->
                    <div class="lg:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                            Organization Address <span class="text-red-500">*</span>
                        </label>
                        <textarea id="address" pTextarea formControlName="address" rows="3" class="w-full"
                            placeholder="Enter complete organization address"
                            [class.ng-invalid]="isFieldInvalid('address')"></textarea>
                        <small *ngIf="isFieldInvalid('address')" class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('address') }}
                        </small>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-phone text-blue-600"></i>
                        Contact Information
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">Provide contact details for communication</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    <!-- Contact Number -->
                    <div>
                        <label for="contactNumber" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Number <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" id="contactNumber" pInputText formControlName="contactNumber"
                            class="w-full h-11" placeholder="+94 XX XXX XXXX"
                            [class.ng-invalid]="isFieldInvalid('contactNumber')">
                        <small *ngIf="isFieldInvalid('contactNumber')" class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('contactNumber') }}
                        </small>
                    </div>

                    <!-- Organization Email -->
                    <div>
                        <label for="organizationEmail" class="block text-sm font-medium text-gray-700 mb-2">
                            Organization Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="organizationEmail" pInputText formControlName="organizationEmail"
                            class="w-full h-11" placeholder="<EMAIL>"
                            [class.ng-invalid]="isFieldInvalid('organizationEmail')">
                        <small *ngIf="isFieldInvalid('organizationEmail')" class="text-red-500 mt-1 block">
                            {{ getFieldErrorMessage('organizationEmail') }}
                        </small>
                    </div>
                </div>
            </div>

            <!-- Organization Qualifications Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="pi pi-users text-blue-600"></i>
                        Organization Qualifications
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">Add qualified persons in your organization</p>
                </div>

                <!-- Added Qualifications List -->
                <div formArrayName="organizationQualification" class="space-y-4"
                    *ngIf="organizationQualificationArray.length > 0">
                    <div *ngFor="let qualification of organizationQualificationArray.controls; let i = index"
                        [formGroupName]="i" class="border border-gray-200 rounded-lg p-4 bg-gray-50">

                        <!-- Qualification Header -->
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-base font-semibold text-gray-800">
                                {{ getQualificationLabel(qualification.get('qualificationId')?.value) }}
                            </h4>
                            <p-button type="button" icon="pi pi-trash"
                                styleClass="p-button-danger p-button-outlined p-button-sm"
                                (onClick)="removeQualification(i)" pTooltip="Remove qualification">
                            </p-button>
                        </div>

                        <!-- Qualification Details -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <span class="block text-sm font-medium text-gray-700 mb-1">Employee Name</span>
                                <p class="text-gray-900">{{ qualification.get('employeeName')?.value }}</p>
                            </div>

                            <div>
                                <span class="block text-sm font-medium text-gray-700 mb-1">Employee Designation</span>
                                <p class="text-gray-900">{{ qualification.get('employeeDesignation')?.value }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Add Qualification Button Below List -->
                    <div class="flex justify-center pt-4">
                        <p-button type="button" icon="pi pi-plus" label="Add Another Qualification" severity="contrast"
                            [disabled]="isLoadingQualifications" (onClick)="addQualification()">
                        </p-button>
                    </div>
                </div>

                <!-- Empty State with Add Button -->
                <div *ngIf="organizationQualificationArray.length === 0"
                    class="text-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
                    <i class="pi pi-users text-gray-400 text-3xl mb-4"></i>
                    <p class="text-gray-600 mb-4">No qualifications added yet</p>
                    <p-button type="button" icon="pi pi-plus" label="Add Qualification" severity="contrast"
                        [disabled]="isLoadingQualifications" (onClick)="addQualification()">
                    </p-button>
                </div><!-- Section Validation Message -->
                <div *ngIf="organizationQualificationArray.invalid && organizationQualificationArray.touched"
                    class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center gap-2">
                        <i class="pi pi-exclamation-triangle text-red-600"></i>
                        <p class="text-sm text-red-800">
                            <span *ngIf="organizationQualificationArray.errors?.['minArrayLength']">
                                Please add at least one qualification to proceed.
                            </span>
                            <span *ngIf="!organizationQualificationArray.errors?.['minArrayLength']">
                                Please complete all required fields for organization qualifications.
                            </span>
                        </p>
                    </div>
                </div>
            </div><!-- Form Actions -->
            <div class="p-4 sm:p-6">
                <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                    <p-button type="button" icon="pi pi-times" label="Cancel"
                        styleClass="p-button-outlined w-full sm:w-auto" (onClick)="onCancel()"
                        [disabled]="isSubmitting">
                    </p-button>

                    <p-button type="submit" icon="pi pi-check"
                        [label]="isUpdateMode ? 'Update Organization' : 'Register Organization'"
                        [disabled]="organizationForm.invalid || isSubmitting" [loading]="isSubmitting"
                        styleClass="p-button-primary w-full sm:w-auto" [style]="{ 'min-width': '180px' }">
                    </p-button>
                </div> <!-- Form Status Message -->
                <div *ngIf="organizationForm.invalid && organizationForm.touched"
                    class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center gap-2">
                        <i class="pi pi-exclamation-triangle text-red-600"></i>
                        <div class="text-sm text-red-800">
                            <p class="mb-2">Please complete the following requirements before submitting:</p>
                            <ul class="list-disc list-inside space-y-1">
                                <li
                                    *ngIf="organizationForm.get('strOrganizationId')?.invalid && organizationForm.get('strOrganizationId')?.touched">
                                    Organization Registration Number is required
                                </li>
                                <li
                                    *ngIf="organizationForm.get('organizationName')?.invalid && organizationForm.get('organizationName')?.touched">
                                    Organization Name is required
                                </li>
                                <li
                                    *ngIf="organizationForm.get('address')?.invalid && organizationForm.get('address')?.touched">
                                    Organization Address is required
                                </li>
                                <li
                                    *ngIf="organizationForm.get('contactNumber')?.invalid && organizationForm.get('contactNumber')?.touched">
                                    Valid Contact Number is required
                                </li>
                                <li
                                    *ngIf="organizationForm.get('organizationEmail')?.invalid && organizationForm.get('organizationEmail')?.touched">
                                    Valid Organization Email is required
                                </li>
                                <li *ngIf="organizationQualificationArray.invalid" class="font-semibold text-red-700">
                                    At least one organization qualification must be added
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Qualification Dialog -->
<p-dialog [(visible)]="showQualificationDialog" header="Add Organization Qualification" [modal]="true" [closable]="true"
    [resizable]="false" [style]="{ width: '600px' }" [dismissableMask]="false">

    <form [formGroup]="qualificationDialogForm" (ngSubmit)="onAddQualificationFromDialog()">
        <div class="space-y-6">
            <!-- Qualification Selection -->
            <div>
                <label for="selectedQualificationId" class="block text-sm font-medium text-gray-700 mb-3">
                    Select Qualification <span class="text-red-500">*</span>
                </label>
                <p class="text-sm text-gray-600 mb-4">
                    Choose the qualification that applies to your organization:
                </p>

                <div class="space-y-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-3">
                    <div *ngFor="let qualification of qualifications"
                        class="flex items-start gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                        [class.border-blue-500]="qualificationDialogForm.get('selectedQualificationId')?.value === qualification.qualificationId"
                        [class.bg-blue-50]="qualificationDialogForm.get('selectedQualificationId')?.value === qualification.qualificationId"
                        (click)="qualificationDialogForm.patchValue({ selectedQualificationId: qualification.qualificationId })">
                        <input type="radio" [id]="'qual-' + qualification.qualificationId"
                            formControlName="selectedQualificationId" [value]="qualification.qualificationId"
                            class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                        <div class="flex-1">
                            <label [for]="'qual-' + qualification.qualificationId"
                                class="font-medium text-gray-900 cursor-pointer">
                                {{ qualification.qualificationName }}
                            </label>
                        </div>
                    </div>
                </div>

                <small *ngIf="isDialogFieldInvalid('selectedQualificationId')" class="text-red-500 block mt-2">
                    Please select a qualification
                </small>
            </div>

            <!-- Employee Name -->
            <div>
                <label for="employeeName" class="block text-sm font-medium text-gray-700 mb-2">
                    Employee Name <span class="text-red-500">*</span>
                </label>
                <input type="text" id="employeeName" pInputText formControlName="employeeName" class="w-full h-11"
                    placeholder="Enter employee full name" [class.ng-invalid]="isDialogFieldInvalid('employeeName')">
                <small *ngIf="isDialogFieldInvalid('employeeName')" class="text-red-500 mt-1 block">
                    Employee name is required
                </small>
            </div>

            <!-- Employee Designation -->
            <div>
                <label for="employeeDesignation" class="block text-sm font-medium text-gray-700 mb-2">
                    Employee Designation <span class="text-red-500">*</span>
                </label>
                <input type="text" id="employeeDesignation" pInputText formControlName="employeeDesignation"
                    class="w-full h-11" placeholder="Enter employee designation/position"
                    [class.ng-invalid]="isDialogFieldInvalid('employeeDesignation')">
                <small *ngIf="isDialogFieldInvalid('employeeDesignation')" class="text-red-500 mt-1 block">
                    Employee designation is required
                </small>
            </div>

            <!-- Note -->
            <div class="flex items-start gap-3">
                <div>
                    <h5 class="font-medium text-yellow-900 mb-1">Note</h5>
                    <p class="text-sm text-yellow-800">
                        Make sure the employee information is accurate as this will be used for verification
                        purposes.
                    </p>
                </div>
            </div>
        </div>

        <!-- Modal Actions -->
        <div class="flex flex-col sm:flex-row gap-3 sm:justify-end mt-6">
            <p-button type="button" icon="pi pi-times" label="Cancel" outlined="true"
                (onClick)="onCancelQualificationDialog()">
            </p-button>

            <p-button type="submit" icon="pi pi-plus" label="Add Qualification" severity="primary"
                [disabled]="qualificationDialogForm.invalid">
            </p-button>
        </div>
    </form>
</p-dialog>