import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  FormArray,
  Validators,
  FormsModule,
  AbstractControl,
  ValidatorFn,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TextareaModule } from 'primeng/textarea';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { MenuItem, MessageService } from 'primeng/api';
import { Subject, takeUntil, finalize } from 'rxjs';
import { TrainingOrganizationService } from '../../../../services/student/training/training-organization.service';
import { QualificationService } from '../../../../services/admin/training/qualification.service';
import { 
  TrainingOrganizationRegistrationNew,
} from '../../../../models/shared/training/training-organization.model';
import { Qualification } from '../../../../models/admin/training/qualification.model';
import { AuthService } from '../../../../services/auth/auth.service';

@Component({
  selector: 'app-training-organization-registration',
  standalone: true,  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    ButtonModule,
    InputTextModule,
    TextareaModule,
    DropdownModule,
    ToastModule,
    BreadcrumbModule,
    ProgressSpinnerModule,
    CheckboxModule,
    DialogModule,
  ],
  providers: [MessageService],
  templateUrl: './training-organization-registration.component.html',
  styleUrl: './training-organization-registration.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingOrganizationRegistrationComponent implements OnInit, OnDestroy {
  private readonly fb = inject(FormBuilder);
  private readonly trainingOrganizationService = inject(TrainingOrganizationService);
  private readonly _authService = inject(AuthService);
  private readonly qualificationService = inject(QualificationService);
  private readonly messageService = inject(MessageService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly destroy$ = new Subject<void>();
  // Component state
  isSubmitting = false;
  isUpdateMode = false;
  organizationId: string | null = null;

  // Dialog state
  showQualificationDialog = false;

  // Dropdown data
  qualifications: Qualification[] = [];
  isLoadingQualifications = false;

  // Form configuration
  organizationForm!: FormGroup;
  qualificationDialogForm!: FormGroup;


  // Breadcrumb configuration
  breadcrumbItems: MenuItem[] = [];
  home: MenuItem = {};

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.initializeForm();
    this.loadDropdownData();
    this.checkRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumb(): void {
    this.home = { icon: 'pi pi-home', routerLink: '/dashboard' };
    this.breadcrumbItems = [
      { label: 'Training', routerLink: '/dashboard/training' },
      { label: 'Organization Registration' },
      { label: this.isUpdateMode ? 'Update' : 'Register' },
    ];
  }
  private initializeForm(): void {
    this.organizationForm = this.fb.group({
      strOrganizationId: ['', [Validators.required]],
      organizationName: ['', [Validators.required]],
      address: ['', [Validators.required]],
      contactNumber: ['', [Validators.required, Validators.pattern(/^(\+94|0)\d{9}$/)]],
      organizationEmail: ['', [Validators.required, Validators.email]],
      organizationQualification: this.fb.array([], [Validators.required, this.minArrayLength(1)])
    });

    this.qualificationDialogForm = this.fb.group({
      selectedQualificationId: ['', Validators.required],
      employeeName: ['', Validators.required],
      employeeDesignation: ['', Validators.required]
    });
  }

  private createQualificationGroup(): FormGroup {
    return this.fb.group({
      qualificationId: ['', Validators.required],
      employeeName: ['', [Validators.required]],
      employeeDesignation: ['', [Validators.required]]
    });
  }
  get organizationQualificationArray(): FormArray {
    return this.organizationForm.get('organizationQualification') as FormArray;
  }  // Custom validator for minimum array length
  private minArrayLength(min: number) {
    return (control: any) => {
      if (control instanceof FormArray && control.length >= min) {
        return null;
      }
      return { minArrayLength: { requiredLength: min, actualLength: control?.length ?? 0 } };
    };
  }
  addQualification(): void {
    this.showQualificationDialog = true;
    this.qualificationDialogForm.reset();
  }
  removeQualification(index: number): void {
    if (this.organizationQualificationArray.length > 0) {
      this.organizationQualificationArray.removeAt(index);
      this.organizationQualificationArray.markAsTouched();
      this.organizationQualificationArray.updateValueAndValidity();
      this.cdr.markForCheck();
    }
  }

  canRemoveQualification(index: number): boolean {
    return this.organizationQualificationArray.length > 0;
  }

  // Dialog methods
  onAddQualificationFromDialog(): void {
    if (this.qualificationDialogForm.valid) {
      const formValue = this.qualificationDialogForm.value;
      
      // Check if qualification already exists
      const existingQual = this.organizationQualificationArray.controls.find(control => 
        control.get('qualificationId')?.value === formValue.selectedQualificationId
      );
      
      if (existingQual) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Duplicate Qualification',
          detail: 'This qualification has already been added'
        });
        return;
      }

      const qualGroup = this.createQualificationGroup();
      qualGroup.patchValue({
        qualificationId: formValue.selectedQualificationId,
        employeeName: formValue.employeeName,
        employeeDesignation: formValue.employeeDesignation
      });
        this.organizationQualificationArray.push(qualGroup);
      this.organizationQualificationArray.markAsTouched();
      this.organizationQualificationArray.updateValueAndValidity();
      this.showQualificationDialog = false;
      this.qualificationDialogForm.reset();
      this.cdr.markForCheck();
      
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Qualification added successfully'
      });
    } else {
      this.markDialogFormGroupTouched();
    }
  }

  onCancelQualificationDialog(): void {
    this.showQualificationDialog = false;
    this.qualificationDialogForm.reset();
  }

  private loadDropdownData(): void {
    this.isLoadingQualifications = true;
    this.cdr.markForCheck();

    this.qualificationService
      .getAllQualificationsToList()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoadingQualifications = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data) => {
          this.qualifications = data;
          this.cdr.markForCheck();
        },
        error: (error) => {
          console.error('Error loading qualifications:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load qualifications',
          });
        },
      });
  }

  private checkRouteParams(): void {
    this.route.queryParams.subscribe((params) => {
      if (params['edit'] && params['id']) {
        this.isUpdateMode = true;
        this.organizationId = params['id'];
        this.updateBreadcrumbForEdit();
        if (this.organizationId) {
          this.loadOrganizationData(this.organizationId);
        }
      }
    });
  }

  private updateBreadcrumbForEdit(): void {
    this.breadcrumbItems = [
      { label: 'Training', routerLink: '/dashboard/training' },
      { label: 'Organizations', routerLink: '/dashboard/training/organizations' },
      { label: 'Update Organization' },
    ];
  }

  private loadOrganizationData(id: string): void {
    this.cdr.markForCheck();

    this.trainingOrganizationService
      .getTrainingOrganizationById(id)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.cdr.markForCheck();
        })
      )
      .subscribe({
        next: (data: any) => {
          this.populateForm(data);
        },
        error: (error: any) => {
          console.error('Error loading organization data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load organization data',
          });
          this.router.navigate(['/dashboard/training/organizations']);
        },
      });
  }
  private populateForm(data: any): void {
    this.organizationForm.patchValue({
      strOrganizationId: data.strOrganizationId,
      organizationName: data.organizationName,
      address: data.address,
      contactNumber: data.contactNumber,
      organizationEmail: data.organizationEmail,
      registeredBy: data.registeredBy,
    });

    // Populate organization qualifications
    if (data.organizationQualification && data.organizationQualification.length > 0) {
      // Clear existing qualifications
      while (this.organizationQualificationArray.length > 0) {
        this.organizationQualificationArray.removeAt(0);
      }

      // Add qualifications from data
      data.organizationQualification.forEach((qual: any) => {
        const qualGroup = this.createQualificationGroup();
        qualGroup.patchValue({
          qualificationId: qual.qualificationId,
          employeeName: qual.employeeName,
          employeeDesignation: qual.employeeDesignation,
        });
        this.organizationQualificationArray.push(qualGroup);
      });
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.organizationForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }
  isQualificationFieldInvalid(index: number, fieldName: string): boolean {
    const field = this.organizationQualificationArray.at(index).get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  isDialogFieldInvalid(fieldName: string): boolean {
    const field = this.qualificationDialogForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }
  getFieldErrorMessage(fieldName: string): string {
    const field = this.organizationForm.get(fieldName);
    if (!field?.errors) return '';

    const errors = field.errors;
    
    if (errors['required']) {
      switch (fieldName) {
        case 'strOrganizationId':
          return 'Business registration number is required';
        case 'organizationName':
          return 'Organization name is required';
        case 'trainingSchema':
          return 'Training schema is required';
        case 'category':
          return 'Category is required';
        case 'address':
          return 'Address is required';
        case 'contactNumber':
          return 'Contact number is required';
        case 'organizationEmail':
          return 'Organization email is required';
        default:
          return 'This field is required';
      }
    }

    if (errors['minlength']) {
      switch (fieldName) {
        case 'strOrganizationId':
          return 'Organization ID must be at least 3 characters long';
        case 'organizationName':
          return 'Organization name must be at least 2 characters long';
        case 'address':
          return 'Address must be at least 10 characters long';
        default:
          return `Minimum length is ${errors['minlength'].requiredLength} characters`;
      }
    }

    if (errors['email']) {
      return 'Please enter a valid email address';
    }

    if (errors['pattern']) {
      if (fieldName === 'contactNumber') {
        return 'Please enter a valid phone number (e.g., +94XXXXXXXXX or 0XXXXXXXXX)';
      }
    }

    return 'Invalid input';
  }

  getQualificationLabel(qualificationId: string): string {
    const qualification = this.qualifications.find(q => q.qualificationId === qualificationId);
    return qualification ? qualification.qualificationName : 'Unknown Qualification';
  }

  onSubmit(): void {
    if (this.organizationForm.valid) {
      this.isSubmitting = true;
      this.cdr.markForCheck();

      const formData = this.organizationForm.value;
      formData.registeredBy = this._authService.getCurrentUser()?.username;

      const organizationData: TrainingOrganizationRegistrationNew = {
        strOrganizationId: formData.strOrganizationId,
        organizationName: formData.organizationName,
        trainingSchema: 'NotDefined',
        category: 'NotDefined',
        address: formData.address,
        contactNumber: formData.contactNumber,
        organizationEmail: formData.organizationEmail,
        registeredBy: formData.registeredBy,
        organizationQualification: formData.organizationQualification,
      };

      const operation = this.isUpdateMode
        ? this.trainingOrganizationService.updateTrainingOrganization(this.organizationId!, organizationData)
        : this.trainingOrganizationService.registerTrainingOrganization(organizationData);

      operation
        .pipe(
          takeUntil(this.destroy$),
          finalize(() => {
            this.isSubmitting = false;
            this.cdr.markForCheck();
          })
        )
        .subscribe({
          next: (response) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: this.isUpdateMode
                ? 'Organization updated successfully'
                : 'Organization registered successfully',
            });

            setTimeout(() => {
              this.router.navigate(['/dashboard/training/training-organization']);
            }, 500);
          },
          error: (error) => {
            console.error('Error submitting organization:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: this.isUpdateMode
                ? 'Failed to update organization'
                : 'Failed to register organization',
            });
          },
        });
    } else {
      this.markFormGroupTouched(this.organizationForm);
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields correctly',
      });
    }
  }
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      }
    });
  }

  private markDialogFormGroupTouched(): void {
    Object.keys(this.qualificationDialogForm.controls).forEach((key) => {
      const control = this.qualificationDialogForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.router.navigate(['/dashboard/training/training-organization']);
  }
}