import { Routes } from '@angular/router';
import { DashboardHomeComponent } from './dashboard-home/dashboard-home.component';
import { JobBankComponent } from './job-bank/job-bank.component';
import { JobPostViewComponent } from './job-post-view/job-post-view.component';
import { HelpComponent } from '../shared/help/help.component';
import { SupervisorComponent } from '../shared/supervisor/supervisor.component';
import { TrainingOrganizationComponent } from './training/training-organization/training-organization.component';
import { TrainingSupervisorComponent } from './training/training-supervisor/training-supervisor.component';
import { TrainingOrganizationRegistrationComponent } from './training/training-organization-registration/training-organization-registration.component';

export const studentRoutes: Routes = [
  {
    path: '',
    component: DashboardHomeComponent,
  },
  {
    path: 'aat-job-bank',
    component: JobBankComponent,
  },
  {
    path: 'job-post',
    component: JobPostViewComponent,
  },
  {
    path: 'help',
    component: HelpComponent,
  },
  {
    path: 'supervisor-registration',
    component: SupervisorComponent,
  },
  {
    path: 'training/training-supervisor',
    component: TrainingSupervisorComponent,
  },
  {
    path: 'training/training-organization',
    component: TrainingOrganizationComponent,
  },
  {
    path: 'training/training-organization-registration',
    component: TrainingOrganizationRegistrationComponent,
  },
];
