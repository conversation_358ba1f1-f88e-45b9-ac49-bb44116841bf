import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { JobBankView } from '../../../models/shared/job-bank/job-bank';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { JobBankService } from '../../../services/shared/job-bank.service';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { FileUploadModule, FileSelectEvent } from 'primeng/fileupload';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { JobApplicationService } from '../../../services/shared/job-application.service';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { InputTextModule } from 'primeng/inputtext';
import { AuthService } from '../../../services/auth/auth.service';

@Component({
  selector: 'app-job-post-view',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    ToastModule,
    ReactiveFormsModule,
    InputTextModule,
    FileUploadModule,
    RouterModule,
  ],
  providers: [MessageService],
  templateUrl: './job-post-view.component.html',
  styleUrl: './job-post-view.component.css',
})
export class JobPostViewComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly jobBankService = inject(JobBankService);
  private readonly messageService = inject(MessageService);
  private readonly fb = inject(FormBuilder);
  private readonly jobApplicationService = inject(JobApplicationService);
  private readonly _authService = inject(AuthService);

  job!: JobBankView;
  jobId!: string;

  applicationForm!: FormGroup;
  userName!: string;
  hasApplied: boolean = false;
  ngOnInit(): void {
    const username = this._authService.getCurrentUser()?.username ?? '';
    this.userName = username.charAt(0).toUpperCase() + username.slice(1);
    this.getJobId();
    this.checkIfApplied();
    this.initializeForm();
  }

  checkIfApplied(): void {
    if (!this.jobId) return;

    this.jobApplicationService
      .checkApplicationSubmission(this.jobId)
      .subscribe({
        next: (response: HttpResponse<Object>) => {
          this.hasApplied = (response.body as any)?.hasApplied === true;
          if (this.hasApplied) {
            this.messageService.add({
              severity: 'info',
              summary: 'Application Status',
              detail: 'You have already applied for this job position.',
            });
          }
        },
        error: (error: HttpErrorResponse) => {
          if (error.status !== 200) {
            console.error('Error checking application status:', error);
          }
        },
      });
  }

  initializeForm(): void {
    this.applicationForm = this.fb.group({
      name: ['', Validators.required],
      nic: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      cv: [null, Validators.required],
    });
  }

  getJobId(): void {
    this.route.queryParams.subscribe((params) => {
      const id = params['jobId'];
      if (id && typeof id === 'string') {
        this.jobId = id;
        this.loadJobDetails(id);
      }
    });
  }

  loadJobDetails(jobId: string): void {
    this.jobBankService.getJobBankById(jobId).subscribe({
      next: (response: JobBankView) => {
        this.job = response;
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load job details',
        });
      },
    });
  }

  onFileChange(event: FileSelectEvent): void {
    if (event.files?.length) {
      const file = event.files[0];

      const isPDF = file.type === 'application/pdf';
      const isUnder2MB = file.size <= 2 * 1024 * 1024; // 2MB in bytes

      if (!isPDF) {
        this.messageService.add({
          severity: 'error',
          summary: 'Invalid File',
          detail: 'Please upload a PDF file only.',
        });
        this.applicationForm.patchValue({ cv: null });
        return;
      }

      if (!isUnder2MB) {
        this.messageService.add({
          severity: 'error',
          summary: 'File Too Large',
          detail: 'File size should be less than 2MB.',
        });
        this.applicationForm.patchValue({ cv: null });
        return;
      }

      // Valid file: patch form
      this.applicationForm.patchValue({ cv: file });
    }
  }

  submitApplication(): void {
    if (this.applicationForm.invalid || !this.job) return;

    const formData = new FormData();
    formData.append('name', this.applicationForm.get('name')?.value);
    formData.append('nic', this.applicationForm.get('nic')?.value);
    formData.append('email', this.applicationForm.get('email')?.value);
    formData.append('cv', this.applicationForm.get('cv')?.value);
    formData.append('AppliedBy', this.userName);

    // Append job-specific info
    formData.append('jobId', this.job.jobId);
    formData.append('companyEmail', this.job.companyEmail);
    formData.append('jobTitle', this.job.jobTitle);

    this.jobApplicationService.submitJobApplication(formData).subscribe({
      next: (res: HttpResponse<Object>) => {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Application submitted successfully!',
        });
        this.applicationForm.reset();
      },
      error: (err: HttpErrorResponse) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to submit application',
        });
      },
    });
  }

  handleImageError(event: Event) {
    const target = event.target;
    if (target && target instanceof HTMLImageElement) {
      target.src = 'assets/company-logo-placeholder.jpg';
    }
  }
}
