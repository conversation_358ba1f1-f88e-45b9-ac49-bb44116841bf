<p-toast></p-toast>

<div class="md:px-10 mt-3">
    <button class="flex items-center gap-2 text-blue-600 hover:text-blue-800 py-2 px-4 md:text-lg lg:text-xl"
        routerLink="/dashboard/aat-job-bank">
        <i class="pi pi-arrow-left md:text-lg lg:text-xl"></i>
        <span class="text-sm md:text-base">Back to Job Bank</span>
    </button>
</div>

<!-- Job details -->
<div class="w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-10 mt-8 md:mt-12 mb-16" *ngIf="job">
    <div class="p-0 max-w-6xl mx-auto">
        <!-- Two-column layout container - flex-col on mobile, flex-row on md+ screens -->
        <div class="flex flex-col md:flex-row gap-16">

            <!-- Left column: Banner - full width on mobile, 40% on desktop -->
            <div class="w-full md:w-2/5 order-1 md:order-1">
                <div class="rounded-md p-2">
                    <img *ngIf="job.bannerUrl" [src]="job.bannerUrl" alt="Job graphic" class="w-full h-auto object-top">
                    <div *ngIf="!job.bannerUrl"
                        class="w-full h-64 md:h-96 flex items-center justify-center bg-gray-100">
                        <span class="text-gray-400">Job Banner Not Available</span>
                    </div>
                </div>
            </div>

            <!-- Right column: Job details - full width on mobile, 60% on desktop -->
            <div class="w-full md:w-3/5 order-2 md:order-2">
                <div class="flex flex-col">
                    <!-- Two-column layout for company logo and job title -->
                    <div class="flex flex-row pt-4 md:pt-0">
                        <!-- Logo -->
                        <div class="w-1/4 flex justify-start mb-4 rounded-5">
                            <div class="relative w-24 h-24 overflow-hidden">
                                <img *ngIf="job.logoUrl" [src]="job.logoUrl" alt="Company Logo"
                                    class="w-full h-full object-cover rounded-4" (error)="handleImageError($event)">
                                <div *ngIf="!job.logoUrl"
                                    class="w-full h-full flex items-center justify-center bg-gray-100">
                                    <i class="pi pi-building text-gray-400 text-xl"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Job title and company details -->
                        <div class="w-3/4">
                            <!-- Header with job title -->
                            <div class="mb-2 text-left">
                                <h2 class="text-xl sm:text-2xl font-bold text-gray-800">{{ job.jobTitle }}</h2>
                            </div>

                            <!-- Company and location info -->
                            <div class="pb-4 flex flex-wrap items-center gap-4">
                                <div class="flex items-center">
                                    <i class="pi pi-building text-blue-500 mr-2"></i>
                                    <span>{{ job.companyName }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="pi pi-map-marker text-red-500 mr-2"></i>
                                    <span>{{ job.location }}</span>
                                </div>
                            </div>

                            <!-- Job type and category -->
                            <div class="pb-4 flex flex-wrap gap-2">
                                <div
                                    class="border border-orange-500 bg-orange-100 text-orange-800 rounded-full px-4 py-1 text-sm text-center">
                                    {{ job.employmentTypeName }}
                                </div>
                                <div
                                    class="border border-blue-500 bg-blue-100 text-blue-800 rounded-full px-4 py-1 text-sm text-center">
                                    {{ job.jobCategory }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Job Description -->
                    <div class="py-4">
                        <h3 class="text-lg font-semibold mb-2">Job Description</h3>
                        <p class="text-gray-700 whitespace-pre-line">{{ job.jobDescription }}</p>
                    </div>

                    <!-- Closing date -->
                    <div class="py-4 flex items-center">
                        <i class="pi pi-calendar text-gray-600 mr-2"></i>
                        <span>Closing Date: {{ job.validUntil | date:'yyyy-MM-dd' }}</span>
                    </div>

                    <!-- Application Form or Already Applied Message -->
                    <div class="pt-6 border-t border-gray-200">
                        <ng-container *ngIf="!hasApplied; else alreadyApplied">
                            <h2 class="text-xl font-bold mb-4">Apply Now</h2>

                            <form [formGroup]="applicationForm" class="p-fluid w-full">
                                <div class="flex flex-col gap-4 mb-6">
                                    <div class="w-full">
                                        <label for="name">Name</label>
                                        <input pInputText id="name" formControlName="name" placeholder="Your name"
                                            class="w-full p-2 border border-gray-300 rounded" autocomplete="off" />
                                    </div>

                                    <div class="w-full">
                                        <label for="nic">NIC</label>
                                        <input pInputText id="nic" formControlName="nic" placeholder="Your NIC"
                                            class="w-full p-2 border border-gray-300 rounded" autocomplete="off" />
                                    </div>

                                    <div class="w-full">
                                        <label for="email">Email</label>
                                        <input pInputText id="email" formControlName="email" placeholder="Your email"
                                            class="w-full p-2 border border-gray-300 rounded" autocomplete="off" />
                                    </div>

                                    <div class="w-full">
                                        <label for="cv">CV</label>
                                        <p-fileUpload id="cv" mode="basic" chooseLabel="Choose CV"
                                            [showCancelButton]="false" [showUploadButton]="false"
                                            accept="application/pdf" (onSelect)="onFileChange($event)"
                                            [style]="{'width':'100%'}" styleClass="p-button-sm custom-file-upload">
                                        </p-fileUpload>
                                        <small class="text-gray-500">Only PDF files are allowed</small>
                                    </div>
                                </div>

                                <div class="flex justify-end gap-4">
                                    <button class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded"
                                        routerLink="/dashboard/aat-job-bank">
                                        Go back
                                    </button>
                                    <p-button label="Apply" severity="primary" (onClick)="submitApplication()"
                                        [disabled]="applicationForm.invalid"></p-button>
                                </div>
                            </form>
                        </ng-container>

                        <ng-template #alreadyApplied>
                            <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="pi pi-info-circle text-blue-500 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium text-blue-800">Application Submitted</h3>
                                        <div class="mt-2 text-blue-700">
                                            <p>You have already applied for this job position. We will contact you if
                                                your resume matches the requirements.</p>
                                        </div>
                                        <div class="mt-4">
                                            <button
                                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
                                                routerLink="/dashboard/aat-job-bank">
                                                Return to Job Bank
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>