::ng-deep .custom-file-upload {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background-color: #fff;
    padding: 10;
    margin-top: 0;
    overflow: hidden;
}

::ng-deep .custom-file-upload .p-button {
    background-color: #1177BB;
    border-color: #1177BB;
    color: white;
    font-size: 0.9rem;
    height: 2.2rem;
    padding: 0.5rem 1rem;
}

::ng-deep .custom-file-upload .p-button:hover {
    background-color: #0F6699;
    border-color: #0F6699;
}

::ng-deep .custom-file-upload .p-fileupload-filename {
    color: #495057;
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

/* Additional styles for the banner container */
@media (min-width: 768px) {
    /* On desktop, ensure the banner section has a minimum height */
    .banner-container {
        min-height: 400px;
    }
}