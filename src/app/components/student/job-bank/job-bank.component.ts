import { Component, inject, OnInit } from '@angular/core';
import { JobBankService } from '../../../services/shared/job-bank.service';
import { CommonModule } from '@angular/common';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { CheckboxModule } from 'primeng/checkbox';
import { PaginatorModule } from 'primeng/paginator';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
} from '@angular/forms';
import { Button } from 'primeng/button';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { DropdownModule } from 'primeng/dropdown';
import { JobBankView } from '../../../models/shared/job-bank/job-bank';
import { AuthService } from '../../../services/auth/auth.service';
import { UserRole } from '../../../models/auth/auth';
import { Router } from '@angular/router';
import { HttpResponse } from '@angular/common/http';
import { MessageService } from 'primeng/api';
import { JobCategoryService } from '../../../services/admin/job-bank/job-category.service';
import { JobCategory } from '../../../models/shared/job-bank/job-category';
import { CardModule } from 'primeng/card';

@Component({
  selector: 'app-job-bank',
  standalone: true,
  providers: [MessageService],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    Button,
    AutoCompleteModule,
    ToastModule,
    ConfirmDialogModule,
    CheckboxModule,
    DropdownModule,
    PaginatorModule,
    CardModule,
  ],
  templateUrl: './job-bank.component.html',
  styleUrls: ['./job-bank.component.css'],
})
export class JobBankComponent implements OnInit {
  private readonly jobBankService = inject(JobBankService);
  private readonly jobCategoryService = inject(JobCategoryService);
  private readonly fb = inject(FormBuilder);
  private readonly router = inject(Router);
  private readonly messageService = inject(MessageService);
  private readonly authService = inject(AuthService);

  jobs: JobBankView[] = [];
  allJobs: JobBankView[] = []; // Store all jobs for filtering
  jobCategories: any[] = [];
  filteredCategories: any[] = [];
  isAdmin: boolean = false;
  filterForm!: FormGroup;
  searchTimeout: any;

  pageNumber: number = 1;
  pageSize: number = 6;
  totalRecords: number = 0;

  showMobileFilters = false;

  ngOnInit(): void {
    this.loadJobs(this.pageNumber, this.pageSize);
    this.initializeForm();
    this.isAdmin =
      this.authService.getCurrentUser()?.roles.includes(UserRole.ADMIN) ||
      false;
    this.loadCategories();
  }

  initializeForm(): void {
    this.filterForm = this.fb.group({
      search: [''],
      fullTime: [false],
      partTime: [false],
      hybrid: [false],
      location: [''],
      jobCategory: [null],
    });
  }

  onPageChange(event: any): void {
    this.pageNumber = Math.floor(event.first / event.rows) + 1;
    this.pageSize = event.rows;
    this.loadJobs(this.pageNumber, this.pageSize);
  }

  loadCategories() {
    this.jobCategoryService.getAllJobCategoriesNoPageAsync().subscribe({
      next: (response: HttpResponse<JobCategory[]>) => {
        if (response.body) {
          this.jobCategories = response.body;
        }
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load job categories',
        });
      },
    });
  }

  loadJobs(pageNumber: number, pageSize: number): void {
    this.jobBankService.getJobBanks(pageNumber, pageSize).subscribe({
      next: (response: HttpResponse<JobBankView[]>) => {
        this.allJobs = response.body!.map((job) => {
            return {
              ...job,
            };
          });

        this.jobs = [...this.allJobs];
        const paginationHeader: string | null =
          response.headers.get('Pagination');
        if (paginationHeader) {
          const pagination = JSON.parse(paginationHeader);
          this.totalRecords = pagination.totalItems;
        }
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load Jobs',
        });
      },
    });
  }

  applyFilters() {
    const filters = this.filterForm.value;
    const searchTerm = filters.search ?? '';
    const location = filters.location ?? '';
    const jobCategory = filters.jobCategory
      ? filters.jobCategory.jobCategoryName
      : '';
    const employmentTypes: number[] = [0, 0, 0];

    if (filters.fullTime) employmentTypes[0] = 1;
    if (filters.partTime) employmentTypes[1] = 1;
    if (filters.hybrid) employmentTypes[2] = 1;

    // Reset the page number when applying new filters
    this.pageNumber = 1;

    this.jobBankService
      .searchJobsWithFilters(
      this.pageNumber,
      this.pageSize,
      searchTerm,
      employmentTypes,
      location,
      jobCategory
      )
      .subscribe({
      next: (response: HttpResponse<any>) => {
        if (response.body?.message) {
        // Handle case when response contains a message (no jobs found)
        this.jobs = [];
        this.totalRecords = 0;
        this.messageService.add({
          severity: 'info',
          summary: 'Information',
          detail: response.body.message
        });
        } else {
        this.jobs = response.body ?? [];

        const paginationHeader = response.headers.get('Pagination');
        if (paginationHeader) {
          const pagination = JSON.parse(paginationHeader);
          this.totalRecords = pagination.totalItems;
        }
        }
      },
      error: (error) => {
        this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to filter jobs',
        });
      },
      });
  }

  resetFilters() {
    this.filterForm.reset({
      search: '',
      fullTime: false,
      partTime: false,
      hybrid: false,
      location: '',
      jobCategory: null,
    });
    this.loadJobs(this.pageNumber, this.pageSize);
  }

  filterCategory(event: any) {
    let query = event.query;
    this.filteredCategories = this.jobCategories.filter((category) =>
      category.jobCategoryName.toLowerCase().includes(query.toLowerCase())
    );
  }

  viewJobPostDetails(jobId: string): void {
    this.router.navigate(['/dashboard/job-post'], {
      queryParams: { jobId: jobId }
    });
  }

  navigateToCreateJobPosting(): void {
    this.router.navigate(['/admin/job-posting/create-job-posting']);
  }

  handleImageError(event: Event) {
    const target = event.target;
    if (target && target instanceof HTMLImageElement) {
      target.src = 'assets/company-logo-placeholder.jpg';
    }
  }

  toggleMobileFilters() {
    this.showMobileFilters = !this.showMobileFilters;
  }
}
