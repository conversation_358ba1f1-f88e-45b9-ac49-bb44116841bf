<div class="w-full -mt-4">
  <div class="h-48 bg-gradient-to-r from-[#198BDB] to-[#262364] flex flex-col justify-center items-center">
    <h1 class="text-4xl font-bold text-white">AAT Job Bank</h1>
    <p class="text-sm text-white">Home / AAT Job Bank</p>
  </div>
</div>

<div class="mb-16 mt-10">
  <div class="container mx-auto p-4 md:px-4">
    <!-- Mobile Filter Toggle Button -->
    <div class="md:hidden mb-4 px-3">
      <p-button 
        label="Filters" 
        icon="pi pi-filter" 
        styleClass="p-button-outlined w-full" 
        (onClick)="toggleMobileFilters()"
        iconPos="right">
        <i [class]="showMobileFilters ? 'pi pi-chevron-up ml-2' : 'pi pi-chevron-down ml-2'"></i>
      </p-button>
    </div>

    <!-- Mobile Filters Dropdown -->
    <div class="md:hidden mb-4 px-3" [class.hidden]="!showMobileFilters">
      <div class="bg-white p-4 rounded-lg shadow border">
        <h2 class="text-lg font-bold mb-3">Filters</h2>
        <form [formGroup]="filterForm" class="space-y-3">
          <div>
            <label for="mobile-search" class="block font-medium text-sm mb-1">Search</label>
            <div class="relative">
              <i class="pi pi-search absolute left-2 top-2.5 text-gray-400 text-sm"></i>
              <input id="mobile-search" type="text" formControlName="search" placeholder="Search jobs"
                class="w-full pl-8 p-1.5 text-sm border border-gray-300 rounded-lg" />
            </div>
          </div>
          <div>
            <label class="block font-medium text-sm mb-1">Job Type</label>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div class="flex items-center">
                <p-checkbox formControlName="fullTime" [binary]="true" inputId="mobile-full-time"></p-checkbox>
                <label for="mobile-full-time" class="ml-2">Full-time</label>
              </div>
              <div class="flex items-center">
                <p-checkbox formControlName="partTime" [binary]="true" inputId="mobile-part-time"></p-checkbox>
                <label for="mobile-part-time" class="ml-2">Part-time</label>
              </div>
              <div class="flex items-center col-span-2">
                <p-checkbox formControlName="hybrid" [binary]="true" inputId="mobile-hybrid"></p-checkbox>
                <label for="mobile-hybrid" class="ml-2">Hybrid</label>
              </div>
            </div>
          </div>
          <div>
            <label for="mobile-location" class="block font-medium text-sm mb-1">Location</label>
            <input id="mobile-location" type="text" formControlName="location" placeholder="City or region"
              class="w-full p-1.5 text-sm border border-gray-300 rounded-lg" />
          </div>
          <div>
            <label for="mobile-category" class="block font-medium text-sm mb-1">Category</label>
            <p-autoComplete id="mobile-category" formControlName="jobCategory" [suggestions]="filteredCategories"
              (completeMethod)="filterCategory($event)" field="jobCategoryName" placeholder="Search category"
              [style]="{'width':'100%'}" [inputStyle]="{'width':'100%', 'fontSize':'0.875rem', 'padding':'0.375rem'}"
              [autoHighlight]="true"></p-autoComplete>
          </div>
          <div class="flex gap-2 pt-2">
            <p-button label="Reset" icon="pi pi-filter-slash" 
              styleClass="p-button-outlined p-button-danger p-button-sm flex-1"
              (onClick)="resetFilters()"></p-button>
            <p-button label="Search" icon="pi pi-search" 
              styleClass="p-button-primary p-button-sm flex-1"
              (onClick)="applyFilters(); toggleMobileFilters()"></p-button>
          </div>
        </form>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Desktop Filters -->
      <div>
        <div class="hidden md:block md:col-span-1 bg-white mb-5 p-3 rounded-lg shadow h-auto">
          <h2 class="text-xl font-bold mb-3">Filters</h2>
          <form [formGroup]="filterForm" class="space-y-3">
            <div>
              <label for="search" class="block font-medium text-sm mb-1">Search</label>
              <div class="relative">
                <i class="pi pi-search absolute left-2 top-2.5 text-gray-400 text-sm"></i>
                <input id="search" type="text" formControlName="search" placeholder="Search jobs"
                  class="w-full pl-8 p-1.5 text-sm border border-gray-300 rounded-lg" />
              </div>
            </div>
            <div>
              <label class="block font-medium text-sm mb-1">Job Type</label>
              <div class="grid grid-cols-1 gap-1 text-sm">
                <div class="flex items-center">
                  <p-checkbox formControlName="fullTime" [binary]="true" inputId="full-Time"></p-checkbox>
                  <label for="fullTime" class="ml-2">Full-time</label>
                </div>
                <div class="flex items-center">
                  <p-checkbox formControlName="partTime" [binary]="true" inputId="part-Time"></p-checkbox>
                  <label for="partTime" class="ml-2">Part-time</label>
                </div>
                <div class="flex items-center">
                  <p-checkbox formControlName="hybrid" [binary]="true" inputId="hybrid"></p-checkbox>
                  <label for="hybrid" class="ml-2">Hybrid</label>
                </div>
              </div>
            </div>
            <div>
              <label for="location" class="block font-medium text-sm mb-1">Location</label>
              <input id="location" type="text" formControlName="location" placeholder="City or region"
                class="w-full p-1.5 text-sm border border-gray-300 rounded-lg" />
            </div>
            <div>
              <label for="category" class="block font-medium text-sm mb-1">Category</label>
              <p-autoComplete id="category" formControlName="jobCategory" [suggestions]="filteredCategories"
                (completeMethod)="filterCategory($event)" field="jobCategoryName" placeholder="Search category"
                [style]="{'width':'100%'}" [inputStyle]="{'width':'100%', 'fontSize':'0.875rem', 'padding':'0.375rem'}"
                [autoHighlight]="true"></p-autoComplete>
            </div>
            <div class="flex justify-between pt-2">
              <p-button label="Reset" icon="pi pi-filter-slash" styleClass="p-button-outlined p-button-danger p-button-sm"
                (onClick)="resetFilters()"></p-button>
              <p-button label="Search" icon="pi pi-search" styleClass="p-button-primary p-button-sm"
                (onClick)="applyFilters()"></p-button>
            </div>
          </form>
        </div>
      </div>

      <div class="md:col-span-3 px-4 md:px-0">
        <div class="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          <div *ngFor="let job of jobs" class="relative">
            <p-card [style]="{ height: '28rem', display: 'flex', flexDirection: 'column' }">
              <ng-template #header>
              <div class="relative h-40 flex items-center justify-center rounded-t-md">
                <img [src]="job.logoUrl" 
                  alt="{{ job.jobTitle }}"
                  class="max-w-full max-h-40 object-contain"
                  (error)="handleImageError($event)" />
                <div class="absolute top-2 right-2 rounded-full w-4 h-4" [ngClass]="{
              'bg-green-500': job.isActive,
              'bg-red-500': !job.isActive
              }"></div>
              </div>
              </ng-template>
              <ng-template #title>
              <div class="text-ellipsis overflow-hidden whitespace-nowrap font-bold">
                {{ job.jobTitle }}
              </div>
              </ng-template>

              <ng-template #content>
              <div class="flex flex-col md: h-36">
                <p class="text-gray-600 mb-4 text-sm line-clamp-3">{{ job.jobDescription }}</p>

                <div class="mt-auto space-y-2 text-sm">
                <div class="flex items-center opacity-75">
                  <i class="pi pi-building text-blue-500 mr-2"></i>
                  <span>{{ job.companyName }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center opacity-75">
                  <i class="pi pi-calendar text-blue-500 mr-2"></i>
                  <span>{{ job.validUntil | date : "yyyy-MM-dd" }}</span>
                  </div>
                  <div class="flex items-center opacity-75">
                  <i class="pi pi-bookmark text-blue-500 mr-2"></i>
                  <span>{{ job.employmentTypeName }}</span>
                  </div>
                </div>
                </div>
              </div>
              </ng-template>

              <ng-template #footer>
                <div class="flex gap-4 mt-5">
                  <button class="flex-1 bg-[#1177BB] text-white py-2 px-3 rounded hover:bg-[#0F6699]" (click)="viewJobPostDetails(job.jobId)">
                    View Details
                  </button>
                </div>
              </ng-template>
            </p-card>
          </div>

          <div *ngIf="jobs.length === 0"
            class="col-span-1 sm:col-span-2 lg:col-span-3 flex items-center justify-center h-64">
            <p class="text-lg text-gray-500 font-medium">
              No jobs available at the moment.
            </p>
          </div>
        </div>

        <div class="mt-8 flex justify-center" *ngIf="jobs.length > 0">
          <p-paginator [rows]="pageSize" [totalRecords]="totalRecords" [showCurrentPageReport]="true"
            [rowsPerPageOptions]="[6, 9, 12]"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} records"
            (onPageChange)="onPageChange($event)"></p-paginator>
        </div>
      </div>
    </div>
  </div>
</div>

<p-toast />