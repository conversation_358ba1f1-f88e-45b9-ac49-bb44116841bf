<p-toast></p-toast>
<div class="w-full rounded-3xl shadow-lg bg-white p-8 hover:-translate-y-1 transition">
  <!-- Updated Tabs section -->
  <div class="tabs">
    <ul class="flex border-b">
      <li (click)="setActiveTab(0)" [ngClass]="{ 'border-b-2 border-primary-blue': activeTab === 0 }"
        class="flex-1 text-center py-2 cursor-pointer">
        Login
      </li>
      <li (click)="setActiveTab(1)" [ngClass]="{ 'border-b-2 border-primary-blue': activeTab === 1 }"
        class="flex-1 text-center py-2 cursor-pointer">
        Register
      </li>
    </ul>
    <div class="p-4">
      <ng-container *ngIf="activeTab === 0">
        <!-- Login form -->
        <form class="space-y-4" [formGroup]="loginForm" (ngSubmit)="login()">
          <div class="form-group">
            <label class="block font-semibold mb-1">Username
              <input type="text" placeholder="Enter your username or student ID"
                class="w-full p-2 border rounded focus:outline-none focus:border-blue-600 font-normal"
                formControlName="username" />
            </label>

          </div>
          <div class="form-group">
            <label class="block font-semibold mb-1">Password
              <input type="password" placeholder="Enter your password"
                class="w-full mt-1 p-2 border rounded focus:outline-none focus:border-blue-600 font-normal"
                formControlName="password" />
            </label>

          </div>
          <div class="form-group flex items-center">
            <label>
              <input type="checkbox" class="mr-1" />
              Remember me</label>
          </div>
          <div class="form-group">
            <a href="#" class="text-primary-blue hover:underline">Forgot password?</a>
          </div>
          <button type="submit" class="w-full bg-primary-blue text-white py-2 rounded hover:brightness-90">
            Login
          </button>
        </form>
      </ng-container>

      <ng-container *ngIf="activeTab === 1">
        <!-- Register form -->
        <form class="space-y-4" [formGroup]="registerForm" (ngSubmit)="register()">
          <div class="form-group">
            <label class="block font-semibold mb-1">Student ID
              <span class="text-sm text-gray-500 ml-1">(This will be use as the username)</span>
              <input type="text" placeholder="Enter your student ID"
                class="w-full mt-1 p-2 border rounded focus:outline-none focus:border-blue-600 font-normal"
                formControlName="username" (input)="validateStudentId($event)" />
              <div class="mt-1 font-normal">
                <small *ngIf="!studentIdValid" class="text-red-500">
                  {{ studentIdValidationMessage }}
                </small>
              </div>
            </label>
          </div>
          <div class="form-group">
            <label class="block font-semibold mb-1">Email
              <input type="email" placeholder="Enter your email"
                class="w-full mt-1 p-2 border rounded focus:outline-none focus:border-blue-600 font-normal"
                formControlName="email" />
            </label>

          </div>
          <div class="form-group">
            <label class="block font-semibold mb-1">Mobile Number
              <input type="text" placeholder="Enter your mobile number"
                class="w-full mt-1 p-2 border rounded focus:outline-none focus:border-blue-600 font-normal"
                formControlName="phoneNumber" />
            </label>

          </div>
          <div class="form-group">
            <label class="block font-semibold mb-1">Password
              <input type="password" placeholder="Enter your password"
                class="w-full mt-1 p-2 border rounded focus:outline-none focus:border-blue-600 font-normal"
                formControlName="password" />
            </label>

          </div>
          <div class="form-group">
            <label class="block font-semibold mb-1">Confirm Password
              <input type="password" placeholder="Confirm your password"
                class="w-full mt-1 p-2 border rounded focus:outline-none focus:border-blue-600 font-normal"
                formControlName="confirmPassword" />
            </label>

            <div *ngIf="registerForm.get('confirmPassword')?.touched">
              <span class="text-red-500 text-sm" *ngIf="registerForm.hasError('passwordMismatch')">
                Passwords don't match
              </span>
            </div>
          </div>
          <button type="submit" class="w-full bg-primary-blue text-white py-2 rounded hover:brightness-90">
            Register
          </button>
        </form>
      </ng-container>
    </div>
  </div>
</div>