import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SelectModule } from 'primeng/select';
import { AuthService } from '../../services/auth/auth.service';
import {
  LoginRequest,
  RegisterRequest,
  User,
  UserRole,
} from '../../models/auth/auth';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ValidatorFn,
  AbstractControl,
  ReactiveFormsModule,
} from '@angular/forms';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { StudentMasterService } from '../../services/student/student-master.service';

@Component({
  selector: 'app-auth',
  standalone: true,
  imports: [CommonModule, SelectModule, ReactiveFormsModule, ToastModule],
  templateUrl: './auth.component.html',
  styleUrl: './auth.component.css',
  providers: [MessageService],
})
export class AuthComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly fb = inject(FormBuilder);
  private readonly router = inject(Router);
  private readonly messageService = inject(MessageService);
  private readonly studentService = inject(StudentMasterService);

  loginRequest: LoginRequest = {} as LoginRequest;
  registerRequest: RegisterRequest = {} as RegisterRequest;
  currentUser: User | null = null;
  loginForm!: FormGroup;
  registerForm!: FormGroup;
  activeTab: number = 0;
  selectedRole: string = '';
  userRoles: any[] = [
    { label: 'Student', value: UserRole.STUDENT },
    { label: 'AAT Staff', value: UserRole.STAFF },
  ];
  showPassword = false;

  // Student ID validation states
  studentIdValidationMessage: string = '';
  studentIdValid: boolean = false;

  ngOnInit(): void {
    this.initializeForms();
  }

  passwordMismatch(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const password = control.get('password')?.value;
      const confirmPassword = control.get('confirmPassword')?.value;
      return password && confirmPassword && password !== confirmPassword
        ? { passwordMismatch: true }
        : null;
    };
  }

  initializeForms(): void {
    this.loginForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
    });

    this.registerForm = this.fb.group(
      {
        username: ['', Validators.required],
        password: ['', Validators.required],
        confirmPassword: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        phoneNumber: ['', Validators.required],
        userRole: [UserRole.STUDENT, Validators.required],
      },
      { validators: this.passwordMismatch() }
    );
  }

  protected setActiveTab(tab: number): void {
    this.activeTab = tab;
  }

  protected onRoleChange(event: any): void {
    if (event) {
      this.selectedRole = event.value;
      this.registerForm.get('userRole')?.setValue(event.value);
    }
  }

  login(): void {
    if (this.loginForm.valid) {
      this.authService.login(this.loginForm.value).then((response) => {
        response.subscribe({
          next: (res) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Login successful',
            });
            this.currentUser = this.authService.getCurrentUser();
            if (this.currentUser?.roles.includes(UserRole.STUDENT)) {
              this.router.navigate(['/dashboard']);
            } else if (
              this.currentUser?.roles.includes(UserRole.ADMIN) ||
              this.currentUser?.roles.includes(UserRole.STAFF)
            ) {
              this.router.navigate(['/admin']);
            } else {
              this.router.navigate(['/']);
            }
          },
          error: (err) => {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Login failed',
            });
          },
        });
      });
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill all required fields',
      });
    }
  }
  register(): void {
    if (this.registerForm.valid && this.studentIdValid) {
      this.authService.register(this.registerForm.value).subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Registration successful, please verify your account',
          });
          const email = this.registerForm.get('email')?.value;
          this.router.navigate(['/auth'], {
            queryParams: { email: email },
          });

          this.registerForm.reset();
        },
        error: (error) => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: error.error.title ?? 'Registration failed',
          });
        },
      });
    } else if (!this.studentIdValid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please enter a valid Student ID',
      });
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill all required fields',
      });
    }
  }

  validateStudentId(event: any): void {
    const value = event.target.value;
    // Reset validation states
    this.studentIdValidationMessage = '';
    this.studentIdValid = false;

    if (!value) {
      this.studentIdValidationMessage = 'Student ID is required';
      return;
    }

    if (value.length > 7) {
      this.studentIdValidationMessage = 'Invalid Student ID';
      return;
    }

    if (value.length == 7) {
      this.studentService.checkValidStudentId(value).subscribe({
        next: (response) => {
          if (response.body.message === "Valid Student ID.") {
            this.studentIdValid = true;
          } else {
            this.studentIdValid = false;
            this.studentIdValidationMessage = 'Invalid Student ID';
          }
        },
        error: () => {
          this.studentIdValid = false;
          this.studentIdValidationMessage = 'Invalid Student ID';
        },
      });
    }
  }
}
