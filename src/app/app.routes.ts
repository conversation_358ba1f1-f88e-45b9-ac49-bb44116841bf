import { Routes } from '@angular/router';

import { MainHomeComponent } from './components/shared/home/<USER>/main-home.component';
import { adminRoutes } from './components/admin/admin.routes';
import { studentRoutes } from './components/student/student.routes';
import { DashboardComponent } from './components/shared/dashboard/dashboard.component';
import { authGuard } from './core/guards/auth.guard';
import { roleGuard } from './core/guards/role.guard';
import { UserRole } from './models/auth/auth';
import { NotFoundComponent } from './components/shared/not-found/not-found.component';
import { HelpComponent } from './components/shared/help/help.component';
import { OtpVerificationComponent } from './components/student/otp-verification/otp-verification.component';

export const routes: Routes = [
  {
    path: '',
    component: MainHomeComponent,
  },
  {
    path: 'auth',
    component: OtpVerificationComponent,
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [authGuard, roleGuard],
    data: { roles: [UserRole.STUDENT] },
    children: studentRoutes,
  },
  {
    path: 'help',
    component: HelpComponent,
  },
  {
    path: 'admin',
    component: DashboardComponent,
    canActivate: [authGuard, roleGuard],
    data: { roles: [UserRole.ADMIN, UserRole.STAFF] },
    children: adminRoutes
  },
  {
    path: '**',
    component: NotFoundComponent,
  },
];
