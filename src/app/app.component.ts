import { Component, OnInit } from '@angular/core';
import { PrimeNG } from 'primeng/config';
import { RouterOutlet } from '@angular/router';
import { NgxSpinnerModule } from 'ngx-spinner';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.css'],
    standalone: true,
    imports: [RouterOutlet, NgxSpinnerModule],
  })
  export class AppComponent implements OnInit {
    constructor(private primeng: PrimeNG) { }

    ngOnInit() {
      this.primeng.ripple.set(true);
    }
  }
