export interface JobBank {
  jobTitle: string;
  jobDescription: string;
  companyCode: string;
  jobCategoryId: string;
  employmentTypeId: string;
  validUntil: string;
  companyEmail: string;
  location: string;
  jobSummary: string;
  bannerBase64?: string;
  fileName?: string;
}

export interface JobBankView {
  jobId: string;
  jobTitle: string;
  jobSummary: string;
  jobDescription: string;
  jobCategory: string;
  jobCategoryId: string;
  companyName: string;
  companyCode: string;
  companyEmail: string;
  location: string;
  employmentTypeId: string;
  employmentTypeName: string;
  validUntil: string;
  isActive: boolean;
  createdAt: string;
  createdBy: string;
  rowVersion: string;
  imageUrl?: string;
  logoUrl?: string;
  bannerUrl: string;
}

export interface JobBankUpdate {
  jobId: string;
  jobTitle: string;
  jobSummary: string;
  jobDescription: string;
  jobCategoryId: string;
  companyName: string;
  logoUrl: string;
  companyCode: string;
  companyEmail: string;
  location: string;
  employmentTypeId: string;
  employmentTypeName: string;
  validUntil: string;
  isActive: boolean;
  createdBy: string;
  rowVersion: string;
  bannerBase64: string;
  fileName: string;
  isBannerChanged: boolean;
  bannerUrl: string;
}
