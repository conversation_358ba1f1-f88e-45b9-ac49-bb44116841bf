export interface SupervisorRegistrationResponse {
  hasRegistration: boolean;
  data?: SupervisorRegistration;
}

export interface SupervisorRegistration {
  id: string;
  name: string;
  designation: string;
  contactNumber: string;
  location: string;
  email: string;
  memberships: string[];
  approvalStatus: 'pending' | 'approved' | 'rejected';
  registeredAt: Date;
  lastUpdated: Date;
  rejectionReason?: string;
}
