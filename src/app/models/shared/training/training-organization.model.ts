// Primary interface for training organization registration
export interface TrainingOrganizationRegistrationNew {
  strOrganizationId: string;
  organizationName: string;
  trainingSchema: string;
  category: string;
  address: string;
  contactNumber: string;
  organizationEmail: string;
  registeredBy: string;
  organizationQualification: OrganizationQualificationRegistration[];
}

// Interface for organization qualification details
export interface OrganizationQualificationRegistration {
  qualificationId: string;
  employeeName: string;
  employeeDesignation: string;
}

export interface TrainingOrganizationViewData {
  organizationId: string;
  strOrganizationId: string;
  organizationName: string;
  trainingSchema: string;
  category: string;
  address: string;
  approvedStatus: string;
  contactNumber: string;
  organizationEmail: string;
  registeredBy: string;
  organizationQualification: OrganizationQualificationViewData[];
}

export interface OrganizationQualificationViewData {
  lineID: string;
  qualificationId: string;
  employeeName: string;
  employeeDesignation: string;
}

// Interface for API response when getting organization details
export interface TrainingOrganizationResponse {
  strOrganizationId: string;
  organizationName: string;
  trainingSchema: string;
  category: string;
  address: string;
  contactNumber: string;
  organizationEmail: string;
  registeredBy: string;
  organizationQualification: OrganizationQualificationRegistration[];
  isVerified?: boolean;
  approvalStatus?: 'pending' | 'approved' | 'rejected';
  registeredAt?: Date;
  lastUpdated?: Date;
  rejectionReason?: string;
}

// Interface for dropdown options
export interface DropdownOption {
  label: string;
  value: string;
}

// Interface for training organization approval
export interface TrainingOrganizationApproval {
  organizationId: string;
  trainingSchema: string;
  category: string;
  userID: string;
  approveStatus: number;
  approvedRemarks: string;
}