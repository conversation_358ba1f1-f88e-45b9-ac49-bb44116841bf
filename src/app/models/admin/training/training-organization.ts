export interface OrganizationDetails {
  pId: string;
  organizationId: string;
  name: string;
  category: string;
  contactPerson: string;
  organizationEmail: string;
  contactNumber: string;
  address: string;
  caSriLankaRegistrationNumber: string;
  organizationRegisterNumber: string;
  registeredBy: string;
  registeredAt: Date;
  submittedAt: Date;
  status: 'pending';
  documentUrl?: string;
  additionalDocuments?: string[];
  notes?: string;
}


// Interface for pending organization approval
export interface PendingOrganization {
  pId: string;
  organizationId: string;
  name: string;
  category: string;
  contactPerson: string;
  organizationEmail: string;
  contactNumber: string;
  address: string;
  caSriLankaRegistrationNumber: string;
  organizationRegisterNumber: string;
  registeredBy: string;
  registeredAt: Date;
  submittedAt: Date;
  status: 'pending';
  documentUrl?: string;
}

// Interface for organization details
export interface OrganizationDetails extends PendingOrganization {
  additionalDocuments?: string[];
  notes?: string;
}

// Interface for approval/rejection response
export interface ApprovalResponse {
  success: boolean;
  message: string;
  organizationId: string;
}

// Interface for rejection reason
export interface RejectionReason {
  organizationId: string;
  reason: string;
  feedback: string;
}

// Interface for monitoring type selection
export interface MonitoringTypeSelection {
  organizationId: string;
  monitoringType: 'monitored' | 'non-monitored';
}


