export interface Company {
    companyCode: string;
    companyName: string;
    address: string;
    contactMobile: string;
    contactFixed: string;
    contactWhatsApp: string;
    contactPerson: string;
    emailAddress: string;
    logoUrl: string;
    description: string;
    moto: string;
    businessType: string;
}

export interface CompanyWithLogo {
    companyName: string;
    address: string;
    contactMobile: string;
    contactFixed: string;
    contactWhatsApp: string;
    contactPerson: string;
    emailAddress: string;
    logoBase64: string;
    fileName: string;
    description: string;
    moto: string;
    businessType: string;
    isLogoChanged: boolean;
    companyCode: string;
    logoUrl:string;
}