export interface LoginRequest {
    username: string;
    password: string;
}

export interface RegisterRequest {
    username: string;
    password: string;
    email: string;
    phoneNumber: string;
    userRole: string;
}

export interface LoginResponse {
    id: string;
    username: string;
    token: string;
    roles: UserRole[];
    expiryTime: string;
}

export interface User {
    id: string;
    username: string;
    roles: UserRole[];
}

export enum UserRole {
    ADMIN = 'Admin',
    USER = 'User',
    STUDENT = 'Student',
    STAFF = 'Staff'
}