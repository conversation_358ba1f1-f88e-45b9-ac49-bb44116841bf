{"name": "aat-student-portal", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.1.1", "@angular/common": "^19.1.1", "@angular/compiler": "^19.1.1", "@angular/core": "^19.1.1", "@angular/forms": "^19.1.1", "@angular/platform-browser": "^19.1.1", "@angular/platform-browser-dynamic": "^19.1.1", "@angular/router": "^19.1.1", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@popperjs/core": "^2.11.8", "@primeng/themes": "^19.0.5", "chart.js": "^4.4.7", "js-cookie": "^3.0.5", "ngx-spinner": "^19.0.0", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primeng": "^19.0.5", "quill": "^2.0.3", "rxjs": "~7.8.0", "tailwindcss": "^3.4.17", "tailwindcss-primeui": "^0.4.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.1", "@angular/cli": "^19.1.1", "@angular/compiler-cli": "^19.1.1", "@types/jasmine": "~5.1.0", "@types/js-cookie": "^3.0.6", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}